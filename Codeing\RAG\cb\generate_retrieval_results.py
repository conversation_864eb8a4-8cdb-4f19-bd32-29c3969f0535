#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成检索结果JSON文件

功能：
1. 读取train.json中的问题
2. 在正确的数据库中执行检索
3. 将问题和检索到的块保存为JSON文件
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from modules.vector_retriever import VectorRetriever

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RetrievalResultGenerator:
    def __init__(self):
        """初始化检索结果生成器"""
        self.retriever = VectorRetriever(db_dir=Path("data/db"))

    def find_target_database(self, query: str, expected_filename: str) -> Optional[Tuple[str, str, int]]:
        """
        找到目标检索数据库
        
        Returns:
            Tu<PERSON>[db_name, project_type, k] or None if company error
        """
        # 获取TOP1数据库
        matched_dbs = self.retriever.matcher.match_databases(query, max_matches=5)
        if not matched_dbs:
            return None
            
        top1_db_name = matched_dbs[0][0]
        top1_project_name = self.retriever.matcher.db_mapping.get(top1_db_name)
        
        if not top1_project_name:
            return None
            
        # 获取TOP1公司名
        top1_company = self.retriever._get_company_from_project_name(top1_project_name)
        if not top1_company:
            return None
            
        # 获取该公司的所有项目
        company_dbs = self.retriever._get_company_databases(top1_company)
        
        # 检查expected_filename属于哪个项目
        expected_filename_clean = expected_filename.replace('.pdf', '')
        
        # 检查是否是TOP1项目
        if expected_filename_clean == top1_project_name:
            return top1_db_name, "TOP1", 15
            
        # 检查是否是同公司其他项目
        for db_name in company_dbs:
            project_name = self.retriever.matcher.db_mapping.get(db_name)
            if project_name and expected_filename_clean == project_name:
                return db_name, "OTHER", 10
                
        # 不是该公司的项目
        return None

    def process_chunks(self, chunks: List) -> Dict:
        """处理检索到的块，只保留内容"""
        result = {}
        
        for i, chunk in enumerate(chunks):
            # 处理Document对象，只保留内容
            chunk_content = chunk.page_content
            result[f'rank{i+1}'] = chunk_content
            
        return result

    def generate_results(self, output_file: str = "retrieval_results.json"):
        """生成检索结果JSON文件"""
        # 读取训练数据
        train_file = Path("data/train.json")
        if not train_file.exists():
            logger.error(f"❌ 训练文件不存在: {train_file}")
            return
            
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
            
        logger.info(f"📚 处理 {len(train_data)} 个样本")
        
        results = []
        successful_samples = 0
        failed_samples = 0
        
        for i, sample in enumerate(train_data):
            logger.info(f"🔄 处理样本 {i+1}/{len(train_data)}")
            
            query = sample['question']
            expected_filename = sample['filename']
            expected_page = sample['page']
            
            # 找到目标数据库
            target_info = self.find_target_database(query, expected_filename)
            if not target_info:
                logger.warning(f"❌ 样本{i+1}: 公司匹配错误")
                failed_samples += 1
                continue
                
            db_name, project_type, k = target_info
            
            # 执行检索
            try:
                chunks = self.retriever.search_in_database(db_name, query, k=k)
                if not chunks:
                    logger.warning(f"❌ 样本{i+1}: 检索无结果")
                    failed_samples += 1
                    continue
                    
                # 处理检索块
                processed_chunks = self.process_chunks(chunks)
                
                # 构建简化结果
                sample_result = {
                    'question': query
                }
                # 添加排名内容
                sample_result.update(processed_chunks)
                
                results.append(sample_result)
                successful_samples += 1
                
            except Exception as e:
                logger.error(f"❌ 样本{i+1}: 检索失败 - {e}")
                failed_samples += 1
        
        # 保存简化结果
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 检索结果已保存到: {output_path}")
        logger.info(f"📊 处理统计:")
        logger.info(f"  - 成功: {successful_samples}")
        logger.info(f"  - 失败: {failed_samples}")
        logger.info(f"  - 成功率: {successful_samples/len(train_data)*100:.1f}%")


def main():
    """主函数"""
    generator = RetrievalResultGenerator()
    generator.generate_results()


if __name__ == "__main__":
    main()