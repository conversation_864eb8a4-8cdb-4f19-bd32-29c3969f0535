import json

def process_training_data(input_path, output_path):
    """
    读取原始训练集，并将其转换为Alpaca格式的指令数据。
    """
    with open(input_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)
    
    processed_data = []
    # 为每个问答对定义一个统一的指令
    instruction = "你是一名专业的财报数据问答助手"
    
    for item in original_data:
        # 将 "question" 映射到 "input"，"answer" 映射到 "output"
        qa_pair = {
            "instruction": instruction,
            "input": item.get("question", ""),
            "output": item.get("answer", "")
        }
        processed_data.append(qa_pair)
        
    # 将处理好的数据写入新的JSON文件
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(processed_data, f, ensure_ascii=False, indent=2)
        
    print(f"数据处理完成，已保存至 {output_path}")

# 执行转换
input_file = "./data/train.json"
output_file = "./data/qa_train.json"
process_training_data(input_file, output_file)