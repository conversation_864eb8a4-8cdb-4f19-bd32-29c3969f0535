import json

def validate_jsonl_file(file_path):
    """
    验证 JSONL 文件是否符合指定要求：
    1. 每行是一个独立的 JSON 对象
    2. 每个对象须包含一个键名为 messages 的数组，数组不能为空
    3. messages 中每个元素必须包含 role 和 content 两个字段
    4. role 只能是 system、user 或 assistant
    5. 如果有 system 角色消息，应在数组首位
    6. 第一条非 system 消息必须是 user 角色
    7. user 和 assistant 角色的消息应当交替、成对出现，不少于 1 对
    """
    errors = []
    valid_roles = {"system", "user", "assistant"}

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                try:
                    # 1. 检查是否为有效的 JSON 对象
                    data = json.loads(line)
                    if not isinstance(data, dict):
                        errors.append(f"第 {line_num} 行：不是有效的 JSON 对象")
                        continue

                    # 2. 检查是否包含 messages 字段且为非空数组
                    if "messages" not in data:
                        errors.append(f"第 {line_num} 行：缺少 'messages' 字段")
                        continue

                    messages = data["messages"]
                    if not isinstance(messages, list):
                        errors.append(f"第 {line_num} 行：'messages' 必须是数组")
                        continue

                    if len(messages) == 0:
                        errors.append(f"第 {line_num} 行：'messages' 数组不能为空")
                        continue

                    # 3. 检查每个消息的格式
                    for msg_idx, message in enumerate(messages):
                        if not isinstance(message, dict):
                            errors.append(f"第 {line_num} 行，消息 {msg_idx + 1}：消息必须是对象")
                            continue

                        # 检查必需字段
                        if "role" not in message:
                            errors.append(f"第 {line_num} 行，消息 {msg_idx + 1}：缺少 'role' 字段")
                            continue

                        if "content" not in message:
                            errors.append(f"第 {line_num} 行，消息 {msg_idx + 1}：缺少 'content' 字段")
                            continue

                        # 4. 检查 role 是否有效
                        role = message["role"]
                        if role not in valid_roles:
                            errors.append(f"第 {line_num} 行，消息 {msg_idx + 1}：role '{role}' 无效，只能是 system、user 或 assistant")
                            continue

                    # 5. 检查 system 消息位置
                    system_indices = [i for i, msg in enumerate(messages) if msg.get("role") == "system"]
                    if system_indices:
                        if system_indices != [0]:  # system 消息必须在首位且只能有一个
                            if len(system_indices) > 1:
                                errors.append(f"第 {line_num} 行：只能有一个 system 角色消息")
                            elif system_indices[0] != 0:
                                errors.append(f"第 {line_num} 行：system 角色消息必须在数组首位")

                    # 6. 检查第一条非 system 消息必须是 user
                    non_system_messages = [msg for msg in messages if msg.get("role") != "system"]
                    if non_system_messages and non_system_messages[0].get("role") != "user":
                        errors.append(f"第 {line_num} 行：第一条非 system 消息必须是 user 角色")

                    # 7. 检查 user 和 assistant 消息交替出现且至少有一对
                    user_assistant_messages = [msg for msg in messages if msg.get("role") in ["user", "assistant"]]

                    if len(user_assistant_messages) < 2:
                        errors.append(f"第 {line_num} 行：user 和 assistant 消息不少于 1 对")
                        continue

                    # 检查交替模式
                    expected_role = "user"
                    for i, msg in enumerate(user_assistant_messages):
                        if msg.get("role") != expected_role:
                            errors.append(f"第 {line_num} 行：user 和 assistant 消息应当交替出现，第 {i + 1} 个应该是 {expected_role}")
                            break
                        expected_role = "assistant" if expected_role == "user" else "user"

                    # 检查是否成对出现（最后一个应该是 assistant）
                    if len(user_assistant_messages) % 2 != 0:
                        errors.append(f"第 {line_num} 行：user 和 assistant 消息应当成对出现")

                except json.JSONDecodeError as e:
                    errors.append(f"第 {line_num} 行：JSON 格式错误 - {str(e)}")

    except FileNotFoundError:
        errors.append(f"文件不存在：{file_path}")
    except Exception as e:
        errors.append(f"读取文件时发生错误：{str(e)}")

    return errors

def process_training_data(input_path, output_path):
    """
    读取原始训练集，并将其转换为Alpaca格式的指令数据。
    """
    with open(input_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)
    
    processed_data = []
    # 为每个问答对定义一个统一的指令
    instruction = "你是一名专业的财报数据问答助手"
    
    for item in original_data:
        # 将 "question" 映射到 "input"，"answer" 映射到 "output"
        qa_pair = {
            "instruction": instruction,
            "input": item.get("question", ""),
            "output": item.get("answer", "")
        }
        processed_data.append(qa_pair)
        
    # 将处理好的数据写入新的JSON文件
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(processed_data, f, ensure_ascii=False, indent=2)
        
    print(f"数据处理完成，已保存至 {output_path}")

# 测试 JSONL 验证功能
def test_jsonl_validation():
    """测试 JSONL 文件验证功能"""
    test_file = "./data/test.jsonl"  # 您可以修改为实际的文件路径

    print("开始验证 JSONL 文件...")
    errors = validate_jsonl_file(test_file)

    if not errors:
        print("✅ JSONL 文件格式验证通过！")
    else:
        print("❌ JSONL 文件格式验证失败，发现以下问题：")
        for error in errors:
            print(f"  - {error}")

    return len(errors) == 0

# 执行转换（原有功能）
def run_data_conversion():
    input_file = "./data/train.json"
    output_file = "./data/qa_train.json"
    process_training_data(input_file, output_file)

if __name__ == "__main__":
    # 您可以选择运行哪个功能
    print("选择要执行的功能：")
    print("1. 验证 JSONL 文件格式")
    print("2. 执行数据格式转换")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "1":
        test_jsonl_validation()
    elif choice == "2":
        run_data_conversion()
    else:
        print("无效选择，默认执行 JSONL 验证")
        test_jsonl_validation()