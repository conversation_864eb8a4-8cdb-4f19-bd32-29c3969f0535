#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练集评估脚本

功能特性：
1. 读取train.json中的问题
2. 使用多进程RAG系统生成答案
3. 验证filename和page匹配度
4. 输出不匹配的结果并统计准确率

作者: AI Assistant
日期: 2025年
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import time
import multiprocessing as mp
import queue
from dotenv import load_dotenv

# 导入主程序模块
from main import RAGPipeline, worker_process, process_single_question

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('evaluate_train.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrainEvaluator:
    """训练集评估器"""
    
    def __init__(self, db_dir: Path, train_file: Path):
        """
        初始化评估器
        
        Args:
            db_dir: 向量数据库目录
            train_file: 训练数据文件路径
        """
        self.db_dir = db_dir
        self.train_file = train_file
        self.pipeline = RAGPipeline(db_dir)
        
        # 加载训练数据
        self.train_data = self.load_train_data()
        logger.info(f"加载了 {len(self.train_data)} 个训练样本")
    
    def load_train_data(self) -> List[Dict[str, Any]]:
        """加载训练数据"""
        try:
            with open(self.train_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载训练数据: {len(data)} 个样本")
            return data
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            return []
    

    
    def evaluate_all_multiprocess(self, max_samples: int = None, start_from: int = 0, 
                                  num_processes: int = 4) -> Dict[str, Any]:
        """
        多进程评估所有样本
        
        Args:
            max_samples: 最大评估样本数，None表示评估全部
            start_from: 从第几个样本开始评估
            num_processes: 进程数量
            
        Returns:
            评估统计结果
        """
        # 确定评估范围
        total_samples = len(self.train_data)
        if max_samples is None:
            end_index = total_samples
        else:
            end_index = min(start_from + max_samples, total_samples)
        
        samples_to_evaluate = self.train_data[start_from:end_index]
        actual_count = len(samples_to_evaluate)
        
        logger.info(f"开始多进程评估 {actual_count} 个样本 (索引 {start_from}-{end_index-1})")
        logger.info(f"使用 {num_processes} 个进程")
        
        start_time = time.time()
        
        # 创建进程间通信队列
        task_queue = mp.Queue()
        result_queue = mp.Queue()
        
        # 将任务放入队列
        for i, sample in enumerate(samples_to_evaluate):
            task_index = start_from + i
            # 将样本转换为与main.py兼容的格式
            # 注意：不传递真实的filename和page，让RAG系统自然推理
            task_data = {
                "question": sample.get("question", ""),
                "filename": "xx.pdf",  # 使用默认值，让RAG系统推理
                "page": 1,  # 使用默认值，让RAG系统推理
                "expected_filename": sample.get("filename", ""),
                "expected_page": sample.get("page", 1)
            }
            task_queue.put((task_index, task_data))
        
        # 启动工作进程
        processes = []
        for worker_id in range(num_processes):
            p = mp.Process(
                target=worker_process,
                args=(worker_id, task_queue, result_queue, self.db_dir)
            )
            p.start()
            processes.append(p)
        
        logger.info(f"已启动 {num_processes} 个工作进程")
        
        # 收集结果
        results = [None] * actual_count
        completed_count = 0
        
        try:
            while completed_count < actual_count:
                try:
                    # 从结果队列获取结果，超时30秒
                    task_index, result = result_queue.get(timeout=30)
                    
                    # 计算在当前批次中的索引
                    batch_index = task_index - start_from
                    if 0 <= batch_index < actual_count:
                        results[batch_index] = result
                        completed_count += 1
                        
                        current_progress = completed_count
                        logger.info(f"进度: {current_progress}/{actual_count}")
                        
                except queue.Empty:
                    logger.warning("等待结果超时，检查进程状态...")
                    # 检查进程是否还活着
                    alive_processes = [p for p in processes if p.is_alive()]
                    if not alive_processes:
                        logger.error("所有工作进程都已退出")
                        break
                    continue
                        
        except KeyboardInterrupt:
            logger.info("\n检测到中断信号，正在停止工作进程...")
            
        finally:
            # 发送结束信号给所有进程
            for _ in range(num_processes):
                task_queue.put(None)
            
            # 等待所有进程结束
            for p in processes:
                p.join(timeout=10)
                if p.is_alive():
                    logger.warning(f"强制终止进程 {p.pid}")
                    p.terminate()
                    p.join()
        
        # 过滤掉None结果并转换为评估格式
        evaluation_results = []
        filename_correct = 0
        page_correct = 0
        both_correct = 0
        success_count = 0
        mismatches = []
        
        # 统计容易出错的文件
        filename_error_count = {}  # {expected_filename: count}
        page_error_count = {}      # {expected_filename: {expected_page: count}}
        
        # 统计文件名预测错误的映射关系
        filename_confusion_matrix = {}  # {expected_filename: {predicted_filename: count}}
        
        for i, result in enumerate(results):
            if result is None:
                continue
                
            sample = samples_to_evaluate[i]
            current_index = start_from + i
            
            expected_filename = sample.get("filename", "")
            expected_page = sample.get("page", 1)
            predicted_filename = result.get("filename", "xx.pdf")
            predicted_page = result.get("page", 1)
            generated_answer = result.get("answer", "")
            
            # 检查匹配情况
            filename_match = (predicted_filename == expected_filename)
            page_match = (predicted_page == expected_page)
            both_match = filename_match and page_match
            
            eval_result = {
                "question": sample.get("question", ""),
                "expected_filename": expected_filename,
                "expected_page": expected_page,
                "predicted_filename": predicted_filename,
                "predicted_page": predicted_page,
                "generated_answer": generated_answer,
                "filename_match": filename_match,
                "page_match": page_match,
                "both_match": both_match,
                "success": True
            }
            
            evaluation_results.append(eval_result)
            success_count += 1
            
            if filename_match:
                filename_correct += 1
            if page_match:
                page_correct += 1
            if both_match:
                both_correct += 1
            
            # 记录不匹配的样本
            if not both_match:
                mismatches.append({
                    "index": current_index,
                    "question": eval_result["question"][:100] + "..." if len(eval_result["question"]) > 100 else eval_result["question"],
                    "expected": f"{expected_filename} 第{expected_page}页",
                    "predicted": f"{predicted_filename} 第{predicted_page}页",
                    "filename_match": filename_match,
                    "page_match": page_match
                })
                
                # 统计文件名错误
                if not filename_match:
                    filename_error_count[expected_filename] = filename_error_count.get(expected_filename, 0) + 1
                    
                    # 统计错误预测的映射关系
                    if expected_filename not in filename_confusion_matrix:
                        filename_confusion_matrix[expected_filename] = {}
                    filename_confusion_matrix[expected_filename][predicted_filename] = \
                        filename_confusion_matrix[expected_filename].get(predicted_filename, 0) + 1
                
                # 统计页码错误
                if not page_match:
                    if expected_filename not in page_error_count:
                        page_error_count[expected_filename] = {}
                    page_key = f"第{expected_page}页"
                    page_error_count[expected_filename][page_key] = page_error_count[expected_filename].get(page_key, 0) + 1
        
        # 计算统计指标
        total_time = time.time() - start_time
        avg_time_per_sample = total_time / success_count if success_count > 0 else 0
        
        filename_accuracy = filename_correct / success_count * 100 if success_count > 0 else 0
        page_accuracy = page_correct / success_count * 100 if success_count > 0 else 0
        both_accuracy = both_correct / success_count * 100 if success_count > 0 else 0
        success_rate = success_count / actual_count * 100 if actual_count > 0 else 0
        
        # 按错误次数排序
        sorted_filename_errors = sorted(filename_error_count.items(), key=lambda x: x[1], reverse=True)
        
        # 整理页码错误统计
        page_error_summary = {}
        for filename, pages in page_error_count.items():
            sorted_pages = sorted(pages.items(), key=lambda x: x[1], reverse=True)
            page_error_summary[filename] = sorted_pages
        
        # 整理文件名混淆矩阵
        filename_confusion_summary = {}
        for expected_file, predictions in filename_confusion_matrix.items():
            sorted_predictions = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
            filename_confusion_summary[expected_file] = sorted_predictions
        
        stats = {
            "total_samples": actual_count,
            "success_count": success_count,
            "filename_correct": filename_correct,
            "page_correct": page_correct,
            "both_correct": both_correct,
            "filename_accuracy": filename_accuracy,
            "page_accuracy": page_accuracy,
            "both_accuracy": both_accuracy,
            "success_rate": success_rate,
            "total_time": total_time,
            "avg_time_per_sample": avg_time_per_sample,
            "mismatches": mismatches,
            "results": evaluation_results,
            "filename_error_stats": sorted_filename_errors,
            "page_error_stats": page_error_summary,
            "filename_confusion_matrix": filename_confusion_summary,
            "error_analysis": {
                "most_confused_files": sorted_filename_errors[:10],
                "total_files_with_errors": len(filename_error_count),
                "total_filename_errors": sum(filename_error_count.values()),
                "total_page_errors": sum(sum(pages.values()) for pages in page_error_count.values()),
                "unique_predicted_files": len(set(pred for preds in filename_confusion_matrix.values() for pred in preds.keys()))
            }
        }
        
        return stats
    
    def save_results(self, stats: Dict[str, Any], output_file: str = "train_evaluation_results.json"):
        """保存评估结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            logger.info(f"评估结果已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def save_predictions(self, stats: Dict[str, Any], output_file: str = "train_predictions.json"):
        """保存预测结果（类似sample_submit.json格式）"""
        try:
            predictions = []
            for result in stats["results"]:
                predictions.append({
                    "filename": result["predicted_filename"],
                    "page": result["predicted_page"],
                    "question": result["question"],
                    "answer": result["generated_answer"]
                })
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(predictions, f, ensure_ascii=False, indent=2)
            logger.info(f"预测结果已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
    
    def save_error_analysis(self, stats: Dict[str, Any], output_file: str = "train_error_analysis.json"):
        """保存错误分析报告"""
        try:
            error_report = {
                "summary": {
                    "total_samples": stats['total_samples'],
                    "success_count": stats['success_count'],
                    "filename_accuracy": stats['filename_accuracy'],
                    "page_accuracy": stats['page_accuracy'],
                    "both_accuracy": stats['both_accuracy']
                },
                "error_analysis": stats.get('error_analysis', {}),
                "most_confused_files": {
                    "description": "按错误次数排序的最容易被预测错误的文件",
                    "data": stats.get('filename_error_stats', [])
                },
                "page_error_breakdown": {
                    "description": "各文件的页码错误详细统计",
                    "data": stats.get('page_error_stats', {})
                },
                "filename_confusion_matrix": {
                    "description": "文件名错误预测映射关系（正确文件名被预测成什么）",
                    "data": stats.get('filename_confusion_matrix', {})
                },
                "detailed_mismatches": {
                    "description": "详细的不匹配样本列表",
                    "data": stats.get('mismatches', [])
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(error_report, f, ensure_ascii=False, indent=2)
            logger.info(f"错误分析报告已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存错误分析报告失败: {e}")
    
    def print_summary(self, stats: Dict[str, Any]):
        """打印评估摘要"""
        print("\n" + "="*60)
        print("评估结果摘要")
        print("="*60)
        print(f"总样本数: {stats['total_samples']}")
        print(f"成功处理: {stats['success_count']} ({stats['success_rate']:.1f}%)")
        print(f"文件名准确率: {stats['filename_accuracy']:.1f}% ({stats['filename_correct']}/{stats['success_count']})")
        print(f"页码准确率: {stats['page_accuracy']:.1f}% ({stats['page_correct']}/{stats['success_count']})")
        print(f"完全匹配准确率: {stats['both_accuracy']:.1f}% ({stats['both_correct']}/{stats['success_count']})")
        print(f"总耗时: {stats['total_time']:.1f}秒")
        print(f"平均每题: {stats['avg_time_per_sample']:.1f}秒")
        print(f"不匹配样本数: {len(stats['mismatches'])}")
        
        # 错误分析统计
        error_analysis = stats.get('error_analysis', {})
        if error_analysis:
            print("\n" + "-"*60)
            print("错误分析统计")
            print("-"*60)
            print(f"出现错误的文件数: {error_analysis['total_files_with_errors']}")
            print(f"文件名错误总次数: {error_analysis['total_filename_errors']}")
            print(f"页码错误总次数: {error_analysis['total_page_errors']}")
            print(f"被错误预测的不同文件数: {error_analysis.get('unique_predicted_files', 0)}")
        
        # 显示最容易出错的文件
        filename_errors = stats.get('filename_error_stats', [])
        if filename_errors:
            print("\n" + "-"*60)
            print("最容易被预测错误的文件 (TOP10):")
            print("-"*60)
            for i, (filename, count) in enumerate(filename_errors[:10]):
                print(f"{i+1:2d}. {filename} (错误次数: {count})")
        
        # 显示文件名错误预测映射关系
        filename_confusion = stats.get('filename_confusion_matrix', {})
        if filename_confusion:
            print("\n" + "-"*60)
            print("文件名错误预测映射 (TOP5 最容易出错的文件):")
            print("-"*60)
            # 按总错误次数排序
            sorted_confusion_files = sorted(filename_confusion.items(), 
                                           key=lambda x: sum(count for _, count in x[1]), 
                                           reverse=True)
            for i, (expected_file, predictions) in enumerate(sorted_confusion_files[:5]):
                total_errors = sum(count for _, count in predictions)
                print(f"{i+1}. 正确文件: {expected_file} (总错误次数: {total_errors})")
                for j, (predicted_file, count) in enumerate(predictions[:3]):  # 显示前3个最常被错误预测的文件
                    print(f"    → 被预测为: {predicted_file} ({count}次)")
        
        # 显示页码错误统计
        page_errors = stats.get('page_error_stats', {})
        if page_errors:
            print("\n" + "-"*60)
            print("页码错误统计 (TOP5 文件):")
            print("-"*60)
            sorted_page_files = sorted(page_errors.items(), 
                                     key=lambda x: sum(count for _, count in x[1]), 
                                     reverse=True)
            for i, (filename, pages) in enumerate(sorted_page_files[:5]):
                total_page_errors = sum(count for _, count in pages)
                print(f"{i+1}. {filename} (总页码错误: {total_page_errors})")
                for page, count in pages[:3]:  # 显示前3个最容易出错的页码
                    print(f"    - {page}: {count}次")
        
        # 显示前10个不匹配的样本
        if stats['mismatches']:
            print("\n" + "-"*60)
            print("不匹配样本示例 (前10个):")
            print("-"*60)
            for i, mismatch in enumerate(stats['mismatches'][:10]):
                print(f"\n{i+1}. 样本索引: {mismatch['index']}")
                print(f"   问题: {mismatch['question']}")
                print(f"   期望: {mismatch['expected']}")
                print(f"   预测: {mismatch['predicted']}")
                if 'error' in mismatch:
                    print(f"   错误: {mismatch['error']}")
                else:
                    print(f"   文件名匹配: {'√' if mismatch['filename_match'] else 'X'}")
                    print(f"   页码匹配: {'√' if mismatch['page_match'] else 'X'}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    # 配置路径
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    train_file = current_dir / "data" / "train.json"
    
    # 检查必要文件
    if not db_dir.exists():
        logger.error(f"向量数据库目录不存在: {db_dir}")
        logger.error("请先运行 04_build_vector_index_v2.py 构建向量索引")
        return
    
    if not train_file.exists():
        logger.error(f"训练数据文件不存在: {train_file}")
        return
    
    print("=" * 60)
    print("训练集评估脚本")
    print("=" * 60)
    
    # 简单的交互式配置
    try:
        # 选择评估样本数
        print("\n请选择评估模式:")
        print("1. 快速测试 (10个样本)")
        print("2. 中等测试 (50个样本)")  
        print("3. 大规模测试 (200个样本)")
        print("4. 完整评估 (全部711个样本)")
        
        choice = input("请输入选择 (1-4, 默认1): ").strip()
        if not choice:
            choice = "1"
            
        sample_configs = {
            "1": 10,
            "2": 50,
            "3": 200,
            "4": None
        }
        
        max_samples = sample_configs.get(choice, 10)
        
        # 选择进程数
        print(f"\n将评估 {'全部' if max_samples is None else max_samples} 个样本")
        num_processes_input = input("请输入进程数 (1-8, 默认4): ").strip()
        try:
            num_processes = int(num_processes_input) if num_processes_input else 4
            num_processes = max(1, min(8, num_processes))  # 限制在1-8之间
        except ValueError:
            num_processes = 4
        
        print(f"使用 {num_processes} 个进程进行评估")
        print("-" * 60)
        
        # 创建评估器
        evaluator = TrainEvaluator(db_dir, train_file)
        
        # 执行多进程评估
        logger.info("开始训练集评估...")
        stats = evaluator.evaluate_all_multiprocess(
            max_samples=max_samples,
            start_from=0,
            num_processes=num_processes
        )
        
        # 保存结果
        evaluator.save_results(stats, "train_evaluation_results.json")
        evaluator.save_predictions(stats, "train_predictions.json")
        evaluator.save_error_analysis(stats, "train_error_analysis.json")
        
        # 打印摘要
        evaluator.print_summary(stats)
        
        logger.info("训练集评估完成！")
        
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Windows多进程支持
    mp.set_start_method('spawn', force=True)
    main()