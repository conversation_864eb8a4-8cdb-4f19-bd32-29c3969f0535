#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量索引构建脚本 V2 - 基于页码分块策略

直接使用 *_content_list.json 作为数据源
使用页码作为父块，文本内容作为子块的新策略
避免MD和JSON之间的复杂匹配问题
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import hashlib
from datetime import datetime
from dotenv import load_dotenv
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
import faiss
import numpy as np
import pickle

# Langchain imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.docstore.document import Document

from langchain_openai import OpenAIEmbeddings

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class PageBasedChunker:
    """基于页码的分块器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        初始化分块器
        
        Args:
            chunk_size: 子块大小
            chunk_overlap: 子块重叠大小
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
        )
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'total_items': 0,
            'total_chunks': 0,
            'text_items': 0,
            'image_items': 0,
            'table_items': 0,
            'other_items': 0
        }
    
    def load_content_list(self, json_file_path: str) -> List[Dict[str, Any]]:
        """
        加载 content_list.json 文件
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            内容列表
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                raise ValueError(f"JSON文件格式错误，应为列表格式: {json_file_path}")
            
            logger.info(f"📄 成功加载 {len(data)} 个内容项: {Path(json_file_path).name}")
            return data
            
        except Exception as e:
            logger.error(f"加载JSON文件失败 {json_file_path}: {e}")
            return []
    
    def group_by_pages(self, content_list: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        按页码分组内容
        
        Args:
            content_list: 内容列表
            
        Returns:
            按页码分组的内容字典
        """
        pages = defaultdict(list)
        
        for item in content_list:
            page_idx = item.get('page_idx', 0)
            pages[page_idx].append(item)
            
            # 统计条目类型
            item_type = item.get('type', 'unknown')
            if item_type == 'text':
                self.stats['text_items'] += 1
            elif item_type == 'image':
                self.stats['image_items'] += 1
            elif item_type == 'table':
                self.stats['table_items'] += 1
            else:
                self.stats['other_items'] += 1
        
        self.stats['total_items'] = len(content_list)
        self.stats['total_pages'] = len(pages)
        
        logger.info(f"📊 内容统计: 总页数={self.stats['total_pages']}, 总条目={self.stats['total_items']}")
        logger.info(f"   类型分布: 文本={self.stats['text_items']}, 图片={self.stats['image_items']}, 表格={self.stats['table_items']}, 其他={self.stats['other_items']}")
        
        return dict(pages)
    
    def create_page_content(self, page_items: List[Dict[str, Any]]) -> str:
        """
        创建页面内容文本
        
        Args:
            page_items: 页面内的所有内容项
            
        Returns:
            合并后的页面文本内容
        """
        content_parts = []
        
        for item in page_items:
            item_type = item.get('type', '')
            
            if item_type == 'text':
                text_content = item.get('text', '').strip()
                if text_content:
                    # 添加文本级别信息
                    text_level = item.get('text_level')
                    if text_level and text_level <= 3:  # 只为主要标题添加标记
                        content_parts.append(f"{'#' * text_level} {text_content}")
                    else:
                        content_parts.append(text_content)
            
            elif item_type == 'image':
                # 添加图片描述信息（不包含脚注以避免污染）
                img_caption = item.get('img_caption', [])
                
                if img_caption:
                    img_text = ' '.join(img_caption)
                    content_parts.append(f"[图片] {img_text}")
                else:
                    content_parts.append("[图片]")
            
            elif item_type == 'table':
                # 添加表格描述和内容（不包含脚注以避免污染）
                table_caption = item.get('table_caption', [])
                table_body = item.get('table_body', '')
                
                table_parts = []
                if table_caption:
                    table_parts.extend(table_caption)
                
                # 处理表格主体内容 - 将HTML表格转换为可读文本
                if table_body:
                    table_text = self._extract_table_text(table_body)
                    if table_text:
                        table_parts.append(table_text)
                
                if table_parts:
                    desc_text = ' '.join(table_parts)
                    content_parts.append(f"[表格] {desc_text}")
                else:
                    content_parts.append("[表格]")
        
        return '\n\n'.join(content_parts)
    
    def _extract_table_text(self, table_html: str) -> str:
        """
        从HTML表格中提取文本内容
        
        Args:
            table_html: HTML表格字符串
            
        Returns:
            提取的表格文本内容
        """
        if not table_html:
            return ""
        
        try:
            import re
            
            # 移除HTML标签，保留内容
            # 将</tr>替换为换行，将</td>替换为制表符
            text = table_html.replace('</tr>', '\n')
            text = text.replace('</td>', '\t')
            text = text.replace('<tr>', '').replace('<td>', '')
            text = text.replace('<table>', '').replace('</table>', '')
            
            # 清理多余的空白字符
            lines = []
            for line in text.split('\n'):
                line = line.strip()
                if line:
                    # 将制表符替换为空格，清理多余空格
                    clean_line = ' '.join(line.split())
                    if clean_line:
                        lines.append(clean_line)
            
            return ' '.join(lines)
            
        except Exception as e:
            # 如果解析失败，返回原始HTML（去掉标签）
            import re
            text = re.sub(r'<[^>]+>', ' ', table_html)
            return ' '.join(text.split())
    
    def chunk_page_content(self, page_content: str, page_idx: int, 
                          project_name: str) -> List[Document]:
        """
        智能段落分块：标题与内容合并，控制在150字符左右
        
        Args:
            page_content: 页面内容
            page_idx: 页码
            project_name: 项目名称
            
        Returns:
            分块后的文档列表
        """
        if not page_content.strip():
            return []
        
        # 按段落分割
        paragraphs = [p.strip() for p in page_content.split('\n\n') if p.strip()]
        if not paragraphs:
            return []
        
        # 智能合并段落
        chunks = self._merge_paragraphs_intelligently(paragraphs)
        
        documents = []
        for i, chunk in enumerate(chunks):
            if chunk.strip():
                doc = Document(
                    page_content=chunk,
                    metadata={
                        'page_number': page_idx + 1,  # 页码从1开始
                        'chunk_index': i,
                        'chunk_type': 'smart_paragraph',
                        'project_name': project_name,
                        'total_chunks_in_page': len(chunks)
                    }
                )
                documents.append(doc)
        
        return documents
    
    def _merge_paragraphs_intelligently(self, paragraphs: List[str], 
                                      target_size: int = 300, 
                                      stop_merge_threshold: int = 100) -> List[str]:
        """
        智能合并段落
        
        Args:
            paragraphs: 段落列表
            target_size: 目标字符数
            stop_merge_threshold: 停止合并阈值（当前块达到此大小且下个段落超过150字符时停止合并）
            
        Returns:
            合并后的块列表
        """
        if not paragraphs:
            return []
        
        chunks = []
        current_chunk = ""
        
        i = 0
        while i < len(paragraphs):
            paragraph = paragraphs[i]
            
            # 如果当前段落是标题（以#开头），必须与下面内容合并
            is_title = paragraph.startswith('#')
            
            # 如果当前块为空，直接添加这个段落
            if not current_chunk:
                current_chunk = paragraph
                i += 1
                continue
            
            # 计算合并后的长度
            merged_length = len(current_chunk) + len(paragraph) + 2  # +2 for \n\n
            
            # 决策是否合并
            should_merge = self._should_merge_paragraph(
                current_chunk, paragraph, merged_length, 
                target_size, stop_merge_threshold, is_title
            )
            
            if should_merge:
                current_chunk += "\n\n" + paragraph
                i += 1
            else:
                # 不合并，保存当前块，开始新块
                chunks.append(current_chunk)
                current_chunk = paragraph
                i += 1
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _should_merge_paragraph(self, current_chunk: str, next_paragraph: str, 
                              merged_length: int, target_size: int, 
                              stop_merge_threshold: int, is_next_title: bool) -> bool:
        """
        判断是否应该合并段落
        
        Args:
            current_chunk: 当前块内容
            next_paragraph: 下一个段落
            merged_length: 合并后的总长度
            target_size: 目标大小
            stop_merge_threshold: 停止合并阈值
            is_next_title: 下一个段落是否为标题
            
        Returns:
            是否应该合并
        """
        current_length = len(current_chunk)
        next_length = len(next_paragraph)
        
        # 如果当前块以标题结尾，必须合并至少一个非标题段落
        current_ends_with_title = any(line.startswith('#') for line in current_chunk.split('\n') if line.strip())
        if current_ends_with_title and current_length < 50:  # 标题块太短，必须合并
            return True
        
        # 如果下一个是标题，优先合并（标题不单独成块）
        if is_next_title:
            return merged_length <= target_size * 2  # 标题可以稍微宽松一些
        
        # 如果合并后不超过目标大小，可以合并
        if merged_length <= target_size:
            return True
        
        # 如果当前块已经达到停止合并阈值，且下一个段落很长，不合并
        if current_length >= stop_merge_threshold and next_length > target_size:
            return False
        
        # 如果当前块很小，即使合并后稍微超出目标大小也要合并
        if current_length < stop_merge_threshold:
            return merged_length <= target_size * 1.5  # 允许超出50%
        
        return False
    
    def process_project(self, json_file_path: str, project_name: str) -> List[Document]:
        """
        处理单个项目
        
        Args:
            json_file_path: content_list.json文件路径
            project_name: 项目名称
            
        Returns:
            处理后的文档列表
        """
        logger.info(f"🔄 开始处理项目: {project_name}")
        
        # 加载内容列表
        content_list = self.load_content_list(json_file_path)
        if not content_list:
            logger.error(f"项目内容为空: {project_name}")
            return []
        
        # 按页码分组
        pages = self.group_by_pages(content_list)
        
        # 处理每一页
        all_documents = []
        
        for page_idx in sorted(pages.keys()):
            page_items = pages[page_idx]
            
            # 创建页面内容
            page_content = self.create_page_content(page_items)
            
            if not page_content.strip():
                logger.debug(f"⚠️ 页面 {page_idx + 1} 内容为空，跳过")
                continue
            
            # 分块页面内容
            page_documents = self.chunk_page_content(page_content, page_idx, project_name)
            all_documents.extend(page_documents)
            
            # 显示进度
            if (page_idx + 1) % 10 == 0:
                logger.info(f"📄 已处理页面: {page_idx + 1}/{self.stats['total_pages']}")
        
        self.stats['total_chunks'] = len(all_documents)
        
        logger.info(f"✅ 项目处理完成: {project_name}")
        logger.info(f"   总页数: {self.stats['total_pages']}")
        logger.info(f"   总块数: {self.stats['total_chunks']}")
        logger.info(f"   平均每页: {self.stats['total_chunks']/self.stats['total_pages']:.1f} 块")
        
        return all_documents


def generate_safe_folder_name(original_name: str) -> str:
    """生成安全的文件夹名称"""
    # 使用MD5哈希生成唯一且安全的文件夹名
    hash_object = hashlib.md5(original_name.encode('utf-8'))
    safe_name = hash_object.hexdigest()
    
    # 添加前缀以便识别
    return f"project_{safe_name}"


def create_vector_index_with_batching(documents: List[Document], embeddings, batch_size: int = 100) -> Optional[Dict]:
    """
    分批创建原生FAISS向量索引，避免API限制
    
    Args:
        documents: 文档列表
        embeddings: 嵌入模型
        batch_size: 批次大小
        
    Returns:
        包含索引、文档和元数据的字典
    """
    if not documents:
        logger.warning("没有文档用于创建向量存储")
        return None
    
    # 如果文档数量不多（≤200），直接处理
    if len(documents) <= 200:
        try:
            # 提取批次文档的文本
            batch_texts = [doc.page_content for doc in documents]
            
            # 获取嵌入向量
            batch_embeddings = embeddings.embed_documents(batch_texts)
            
            # 创建原生FAISS索引（使用IndexFlatIP for cosine similarity）
            embeddings_array = np.array(batch_embeddings, dtype=np.float32)
            dimension = len(batch_embeddings[0])
            index = faiss.IndexFlatIP(dimension)  # 内积相似度（余弦相似度）
            index.add(embeddings_array)
            
            logger.info(f"原生FAISS索引创建完成，处理 {len(documents)} 个文档")
            logger.info(f"索引类型: IndexFlatIP, 维度: {dimension}, 向量数量: {index.ntotal}")
            
            return {
                'index': index,
                'texts': batch_texts,
                'metadatas': [doc.metadata for doc in documents],
                'embedding_dimension': dimension
            }
        except Exception as e:
            logger.error(f"直接创建向量索引失败: {e}，回退到分批处理")
    
    # 文档数量较多，使用分批处理
    logger.info(f"文档数量 {len(documents)}，使用批次大小 {batch_size} 进行分批处理")
    
    all_embeddings = []
    all_texts = []
    all_metadatas = []
    success_count = 0
    
    for i in range(0, len(documents), batch_size):
        batch_docs = documents[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(documents) + batch_size - 1) // batch_size
        
        try:
            # 提取批次文档的文本
            batch_texts = [doc.page_content for doc in batch_docs]
            
            # 获取嵌入向量
            batch_embeddings = embeddings.embed_documents(batch_texts)
            
            # 收集数据
            all_embeddings.extend(batch_embeddings)
            all_texts.extend(batch_texts)
            all_metadatas.extend([doc.metadata for doc in batch_docs])
            
            success_count += len(batch_docs)
            logger.info(f"批次 {batch_num}/{total_batches} 成功，已处理 {success_count} 个文档")
            
            # 批次间休息，避免API限流
            if i + batch_size < len(documents):
                time.sleep(2)  # 增加休息时间到2秒
                
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"批次 {batch_num} 失败: {error_msg}")
            
            # 如果是批次大小错误，尝试减少批次大小并重试
            if "batch size" in error_msg or "maximum allowed" in error_msg or "too many" in error_msg:
                if batch_size > 20:
                    smaller_batch_size = max(20, batch_size // 2)
                    logger.info(f"检测到批次大小限制，尝试减少批次大小到 {smaller_batch_size}")
                    
                    # 重置并使用更小的批次大小重试
                    return create_vector_index_with_batching(documents, embeddings, smaller_batch_size)
            
            logger.warning(f"跳过批次 {batch_num}")
            continue
    
    if not all_embeddings:
        logger.error("没有成功获取任何嵌入向量")
        return None
    
    # 创建原生FAISS索引（使用IndexFlatIP for cosine similarity）
    embeddings_array = np.array(all_embeddings, dtype=np.float32)
    dimension = len(all_embeddings[0])
    index = faiss.IndexFlatIP(dimension)  # 内积相似度（余弦相似度）
    index.add(embeddings_array)
    
    logger.info(f"原生FAISS索引创建完成，成功处理 {success_count}/{len(documents)} 个文档")
    logger.info(f"索引类型: IndexFlatIP, 维度: {dimension}, 向量数量: {index.ntotal}")
    
    return {
        'index': index,
        'texts': all_texts,
        'metadatas': all_metadatas,
        'embedding_dimension': dimension
    }


def build_vector_index(documents: List[Document], project_name: str, 
                      output_dir: str = "data/db", chunk_stats: Dict = None, force_rebuild: bool = False) -> bool:
    """
    构建向量索引
    
    Args:
        documents: 文档列表
        project_name: 项目名称
        output_dir: 输出目录
        chunk_stats: 分块统计信息
        force_rebuild: 是否强制重建
        
    Returns:
        是否成功构建
    """
    if not documents:
        logger.error("文档列表为空，无法构建索引")
        return False
    
    # 使用安全的文件夹名作为存储路径（解决中文路径问题）
    safe_folder_name = generate_safe_folder_name(project_name)
    project_db_dir = Path(output_dir) / safe_folder_name
    
    # 检查是否已存在完整的数据库（断点续传功能）
    if not force_rebuild and project_db_dir.exists():
        faiss_file = project_db_dir / "index.faiss"
        texts_file = project_db_dir / "texts.pkl"
        metadatas_file = project_db_dir / "metadatas.pkl"
        metadata_file = project_db_dir / "metadata.json"
        
        if all(f.exists() for f in [faiss_file, texts_file, metadatas_file, metadata_file]):
            logger.info(f"✅ 项目 {project_name} 的向量数据库已存在，跳过构建")
            return True
        else:
            logger.warning(f"⚠️ 项目 {project_name} 的向量数据库不完整，重新构建")
    elif project_db_dir.exists() and force_rebuild:
        logger.info(f"🔄 强制重建项目 {project_name} 的向量数据库")
    
    try:
        # 初始化嵌入模型（与原始脚本保持一致）
        embedding_model = os.getenv('EMBEDDING_MODEL', 'Qwen/Qwen3-Embedding-8B')
        cn_base_url = os.getenv('GLOBAL_BASE_URL', 'https://api.siliconflow.cn/v1')
        cn_api_key = os.getenv('GLOBAL_API_KEY')
        
        if not cn_api_key:
            raise ValueError("请在 .env 文件中设置 GLOBAL_API_KEY")
        
        logger.info(f"🤖 初始化嵌入模型: {embedding_model}")
        
        # 使用 OpenAI 兼容的嵌入模型
        embeddings = OpenAIEmbeddings(
            model=embedding_model,
            openai_api_base=cn_base_url,
            openai_api_key=cn_api_key
        )
        logger.info(f"✅ 成功初始化嵌入模型: {embedding_model}")
        
        # 构建FAISS索引（使用分批处理）
        logger.info(f"🔨 开始构建向量索引，文档数量: {len(documents)}")
        
        vector_data = create_vector_index_with_batching(documents, embeddings, batch_size=100)
        
        if vector_data is None:
            logger.error(f"无法为项目 {project_name} 创建向量索引")
            return False
        
        # 创建目录并保存索引
        project_db_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存原生FAISS索引
        faiss_file = project_db_dir / "index.faiss"
        faiss.write_index(vector_data['index'], str(faiss_file))
        
        # 保存文档和元数据
        texts_file = project_db_dir / "texts.pkl"
        with open(texts_file, 'wb') as f:
            pickle.dump(vector_data['texts'], f)
        
        metadatas_file = project_db_dir / "metadatas.pkl"  
        with open(metadatas_file, 'wb') as f:
            pickle.dump(vector_data['metadatas'], f)
        
        # 保存项目元数据
        metadata = {
            'project_name': project_name,  # 保存原始中文项目名
            'safe_folder_name': safe_folder_name,  # 保存安全文件夹名
            'total_documents': len(documents),
            'embedding_model': embedding_model,
            'chunk_strategy': 'page_based_v2',
            'created_at': datetime.now().isoformat(),
            'index_type': 'faiss_native_IndexFlatIP',
            'embedding_dimension': vector_data['embedding_dimension'],
            'api_base_url': cn_base_url
        }
        
        # 添加分块统计信息（如果提供）
        if chunk_stats:
            metadata.update({
                'total_pages': chunk_stats.get('total_pages', 0),
                'chunk_stats': chunk_stats
            })
        
        metadata_file = project_db_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 向量索引构建完成: {project_db_dir}")
        return True
        
    except Exception as e:
        logger.error(f"构建向量索引失败: {e}")
        return False


def process_project_worker(project_dir: str) -> Tuple[bool, str, Dict]:
    """
    多进程工作函数 - 处理单个项目
    
    Args:
        project_dir: 项目目录路径
        
    Returns:
        (是否成功, 项目名称, 统计信息)
    """
    project_path = Path(project_dir)
    project_name = project_path.name
    
    # 为子进程设置独立的日志
    logger = logging.getLogger(f"worker_{mp.current_process().pid}")
    
    try:
        if not project_path.exists() or not project_path.is_dir():
            return False, project_name, {"error": f"项目目录不存在: {project_dir}"}
        
        # 查找 *_content_list.json 文件
        json_files = list(project_path.glob('*_content_list.json'))
        if not json_files:
            return False, project_name, {"error": f"未找到 *_content_list.json 文件"}
        
        if len(json_files) > 1:
            logger.warning(f"找到多个 JSON 文件，使用第一个: {json_files[0]}")
        
        json_file = json_files[0]
        
        # 初始化分块器
        chunker = PageBasedChunker(chunk_size=1000, chunk_overlap=200)
        
        # 处理项目
        documents = chunker.process_project(str(json_file), project_name)
        
        if not documents:
            return False, project_name, {"error": "未生成任何文档", "stats": chunker.stats}
        
        # 构建向量索引
        success = build_vector_index(documents, project_name, chunk_stats=chunker.stats, force_rebuild=False)
        
        if success:
            return True, project_name, {
                "success": True,
                "stats": chunker.stats,
                "documents_count": len(documents)
            }
        else:
            return False, project_name, {"error": "向量索引构建失败", "stats": chunker.stats}
            
    except Exception as e:
        return False, project_name, {"error": f"处理异常: {str(e)}"}


def process_single_project(project_dir: str) -> bool:
    """
    处理单个项目目录
    
    Args:
        project_dir: 项目目录路径
        
    Returns:
        是否成功处理
    """
    project_path = Path(project_dir)
    
    if not project_path.exists() or not project_path.is_dir():
        logger.error(f"项目目录不存在: {project_dir}")
        return False
    
    # 查找 *_content_list.json 文件
    json_files = list(project_path.glob('*_content_list.json'))
    if not json_files:
        logger.error(f"未找到 *_content_list.json 文件: {project_dir}")
        return False
    
    if len(json_files) > 1:
        logger.warning(f"找到多个 JSON 文件，使用第一个: {json_files[0]}")
    
    json_file = json_files[0]
    project_name = project_path.name
    
    logger.info(f"🚀 开始处理项目: {project_name}")
    
    # 初始化分块器
    chunker = PageBasedChunker(chunk_size=1000, chunk_overlap=200)
    
    # 处理项目
    documents = chunker.process_project(str(json_file), project_name)
    
    if not documents:
        logger.error(f"项目处理失败，未生成任何文档: {project_name}")
        return False
    
    # 构建向量索引
    success = build_vector_index(documents, project_name, chunk_stats=chunker.stats, force_rebuild=False)
    
    if success:
        logger.info(f"🎉 项目完成: {project_name}")
    else:
        logger.error(f"❌ 项目失败: {project_name}")
    
    return success


def process_all_projects(data_dir: str = "data", workers: int = None) -> Tuple[int, int]:
    """
    处理所有项目（支持多进程）
    
    Args:
        data_dir: 数据目录路径
        workers: 并发进程数，None为自动选择
        
    Returns:
        (成功数量, 总数量)
    """
    parser_dir = Path(data_dir) / 'parser'
    
    if not parser_dir.exists():
        logger.error(f"parser 目录不存在: {parser_dir}")
        return 0, 0
    
    project_dirs = [d for d in parser_dir.iterdir() if d.is_dir()]
    
    if not project_dirs:
        logger.error(f"未找到任何项目目录: {parser_dir}")
        return 0, 0
    
    total_count = len(project_dirs)
    
    # 确定进程数
    if workers is None:
        workers = min(4, mp.cpu_count(), total_count)  # 最多4个进程
    else:
        workers = min(workers, total_count)
    
    logger.info(f"🚀 开始处理 {total_count} 个项目，使用 {workers} 个进程...")
    
    if workers == 1:
        # 单进程模式（回退到原始逻辑）
        logger.info("📝 使用单进程模式")
        return _process_projects_sequential(project_dirs)
    
    # 多进程模式
    logger.info(f"⚡ 使用多进程模式: {workers} 个并发进程")
    start_time = time.time()
    
    success_count = 0
    failed_projects = []
    completed_count = 0
    
    try:
        with ProcessPoolExecutor(max_workers=workers) as executor:
            # 提交所有任务
            future_to_project = {
                executor.submit(process_project_worker, str(project_dir)): project_dir.name 
                for project_dir in project_dirs
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_project):
                project_name = future_to_project[future]
                completed_count += 1
                
                try:
                    success, returned_name, stats = future.result()
                    
                    if success:
                        success_count += 1
                        logger.info(f"✅ [{completed_count}/{total_count}] 成功: {returned_name}")
                        
                        # 显示统计信息
                        if "stats" in stats:
                            project_stats = stats["stats"]
                            logger.info(f"   📊 页数: {project_stats.get('total_pages', 0)}, "
                                      f"块数: {project_stats.get('total_chunks', 0)}, "
                                      f"文档数: {stats.get('documents_count', 0)}")
                    else:
                        failed_projects.append((returned_name, stats.get("error", "未知错误")))
                        logger.error(f"❌ [{completed_count}/{total_count}] 失败: {returned_name}")
                        logger.error(f"   错误: {stats.get('error', '未知错误')}")
                    
                    # 显示进度
                    progress = (completed_count / total_count) * 100
                    logger.info(f"📈 进度: {completed_count}/{total_count} ({progress:.1f}%) | "
                              f"成功: {success_count} | 失败: {len(failed_projects)}")
                    
                except Exception as e:
                    failed_projects.append((project_name, f"结果处理异常: {str(e)}"))
                    logger.error(f"❌ [{completed_count}/{total_count}] 异常: {project_name} - {e}")
    
    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断处理")
        return success_count, completed_count
    except Exception as e:
        logger.error(f"多进程处理异常: {e}")
        return success_count, completed_count
    
    # 处理完成统计
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"\n🎉 全部处理完成！")
    logger.info(f"📊 总结统计:")
    logger.info(f"   ✅ 成功: {success_count}/{total_count} ({(success_count/total_count)*100:.1f}%)")
    logger.info(f"   ❌ 失败: {len(failed_projects)}")
    logger.info(f"   ⏱️ 总用时: {duration:.1f}秒")
    logger.info(f"   🚀 平均速度: {total_count/duration:.1f} 项目/秒")
    
    # 显示失败项目详情
    if failed_projects:
        logger.warning(f"\n⚠️ 失败项目详情:")
        for project_name, error in failed_projects[:10]:  # 最多显示10个
            logger.warning(f"   - {project_name}: {error}")
        if len(failed_projects) > 10:
            logger.warning(f"   ... 还有 {len(failed_projects) - 10} 个失败项目")
    
    return success_count, total_count


def _process_projects_sequential(project_dirs: List[Path]) -> Tuple[int, int]:
    """
    顺序处理项目（单进程模式）
    
    Args:
        project_dirs: 项目目录列表
        
    Returns:
        (成功数量, 总数量)
    """
    success_count = 0
    total_count = len(project_dirs)
    
    for i, project_dir in enumerate(project_dirs, 1):
        try:
            logger.info(f"\n📁 [{i}/{total_count}] 处理项目: {project_dir.name}")
            
            if process_single_project(str(project_dir)):
                success_count += 1
                logger.info(f"✅ 成功完成: {project_dir.name}")
            else:
                logger.error(f"❌ 处理失败: {project_dir.name}")
            
            logger.info(f"📊 当前进度: {success_count}/{i} 成功")
            print("-" * 80)
            
        except Exception as e:
            logger.error(f"处理项目时出错 {project_dir}: {e}")
    
    logger.info(f"🎉 全部处理完成！成功: {success_count}/{total_count}")
    return success_count, total_count


def main():
    """主函数 - 纯净版本，无命令行参数"""
    logger.info("🚀 开始构建基于页码的向量索引...")
    
    # 检查环境变量
    required_env_vars = ['GLOBAL_API_KEY']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ 缺少必要的环境变量: {missing_vars}")
        logger.error("请在 .env 文件中设置这些变量")
        return
    
    # 固定配置
    data_dir = "data"
    workers = 4  # 使用4个进程，提高构建速度
    
    logger.info(f"📁 数据目录: {data_dir}")
    logger.info(f"⚡ 并发进程数: {workers}")
    
    try:
        # 处理所有项目
        success_count, total_count = process_all_projects(data_dir, workers=workers)
        
        if success_count == total_count:
            logger.info("🎉 所有项目处理完成！")
        else:
            logger.warning(f"⚠️ 部分项目失败：{success_count}/{total_count} 成功")
            
    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断")
    except Exception as e:
        logger.error(f"❌ 处理过程中出现错误: {e}")
        raise


if __name__ == '__main__':
    main()