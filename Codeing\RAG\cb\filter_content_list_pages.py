#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Content List 页面过滤器
过滤 parser/ 目录下 *_content_list.json 文件中的目录和声明信息

功能：
1. page_idx 1-6 (第2-7页)：过滤目录相关信息，删除整个目录页面
2. 后2页：过滤免责声明、联系方式等信息（基于 text_level=1 的标题匹配）

作者: AI Assistant  
日期: 2025年
"""

import re
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ContentListPageFilter:
    """Content List 页面过滤器"""
    
    def __init__(self):
        # page_idx 1-6 目录相关关键词
        self.toc_keywords = [
            '目录',
            '内容目录', 
            '正文目录', 
            '图表目录',
            '本期目录',
            '图目录',
            '表目录',
            '附录目录',
            '文章目录',
            '报告目录',
            'contents',
            'table of contents',
        ]
        
        # 后2页声明/联系相关关键词
        self.disclaimer_keywords = [
            '免责声明',
            '风险提示',
            '版权声明',
            '联系方式',
            '分析师声明',
            '重要声明',
            '法律声明',
            '声明',
            '投资建议',
            '风险警示',
            '联系我们',
            '公司联系方式',
            '分析师信息',
            '研究团队',
            '机构销售',
            '客服电话',
            '官方网站',
            '地址',
            '电话',
            '邮箱',
            '联络',
            '联系',
            'disclaimer',
            'risk warning',
            'contact',
            'analyst',
        ]
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'total_pages_filtered': 0,
            'toc_pages_removed': 0,
            'disclaimer_sections_removed': 0,
            'total_items_removed': 0
        }
    
    def is_target_keyword_title(self, item: Dict[str, Any], keywords: List[str]) -> bool:
        """
        判断是否是目标关键词标题
        
        Args:
            item: content_list 中的项目
            keywords: 关键词列表
            
        Returns:
            是否匹配
        """
        # 必须是 text_level = 1 的文本项
        if (item.get('type') != 'text' or 
            item.get('text_level') != 1):
            return False
        
        text_content = item.get('text', '').strip()
        if not text_content:
            return False
        
        # 检查关键词匹配
        text_lower = text_content.lower()
        for keyword in keywords:
            keyword_lower = keyword.lower()
            if (keyword_lower in text_lower or 
                text_lower.endswith(keyword_lower) or
                text_lower.startswith(keyword_lower)):
                return True
        
        return False
    
    def find_next_level1_title_index(self, items: List[Dict[str, Any]], start_index: int) -> int:
        """
        找到下一个 text_level=1 标题的索引
        
        Args:
            items: 页面项目列表
            start_index: 搜索起始索引
            
        Returns:
            下一个 text_level=1 标题的索引，如果没有找到返回 len(items)
        """
        for i in range(start_index + 1, len(items)):
            item = items[i]
            if (item.get('type') == 'text' and 
                item.get('text_level') == 1):
                return i
        return len(items)
    
    def filter_page_content(self, page_items: List[Dict[str, Any]], keywords: List[str], 
                           filter_type: str) -> Tuple[List[Dict[str, Any]], int]:
        """
        过滤页面内容
        
        Args:
            page_items: 页面项目列表
            keywords: 过滤关键词列表
            filter_type: 过滤类型（'toc' 或 'disclaimer'）
            
        Returns:
            (过滤后的项目列表, 删除的项目数量)
        """
        if not page_items:
            return page_items, 0
        
        # 检查页面中是否有目标关键词标题
        has_target_keyword = False
        for item in page_items:
            if self.is_target_keyword_title(item, keywords):
                logger.debug(f"发现{filter_type}标题: {item.get('text', '')}")
                has_target_keyword = True
                break
        
        # 如果是目录页面，删除整个页面
        if has_target_keyword and filter_type == 'toc':
            logger.debug(f"删除整个目录页面，共 {len(page_items)} 个项目")
            # 统计删除的页面数
            self.stats['toc_pages_removed'] += 1
            return [], len(page_items)
        
        # 如果是声明页面，使用原来的逻辑（按章节删除）
        elif has_target_keyword and filter_type == 'disclaimer':
            filtered_items = []
            removed_count = 0
            i = 0
            
            while i < len(page_items):
                current_item = page_items[i]
                
                # 检查是否是目标关键词标题
                if self.is_target_keyword_title(current_item, keywords):
                    logger.debug(f"发现{filter_type}标题: {current_item.get('text', '')}")
                    
                    # 找到下一个 text_level=1 标题的位置
                    next_title_index = self.find_next_level1_title_index(page_items, i)
                    
                    # 删除从当前标题到下一个标题之间的所有内容
                    removed_items = next_title_index - i
                    removed_count += removed_items
                    
                    logger.debug(f"删除{filter_type}区间: 索引 {i}-{next_title_index-1} ({removed_items} 个项目)")
                    
                    # 跳过这个区间
                    i = next_title_index
                    
                    # 统计删除的章节数
                    self.stats['disclaimer_sections_removed'] += 1
                else:
                    # 保留当前项目
                    filtered_items.append(current_item)
                    i += 1
            
            return filtered_items, removed_count
        
        # 没有目标关键词，保留所有内容
        return page_items, 0
    
    def filter_content_list(self, content_list: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Dict[str, int]]:
        """
        过滤整个 content_list
        
        Args:
            content_list: 原始内容列表
            
        Returns:
            (过滤后的内容列表, 统计信息)
        """
        if not content_list:
            return content_list, {}
        
        # 按页码分组
        pages = defaultdict(list)
        for item in content_list:
            page_idx = item.get('page_idx', 0)
            pages[page_idx].append(item)
        
        # 找到最大页码
        max_page_idx = max(pages.keys()) if pages else 0
        
        logger.info(f"文档总页数: {max_page_idx + 1}, 页码范围: 0-{max_page_idx}")
        
        filtered_content = []
        total_removed = 0
        pages_filtered = 0
        
        for page_idx in sorted(pages.keys()):
            page_items = pages[page_idx]
            page_num = page_idx + 1  # 页码从1开始显示
            
            # page_idx 1-6 过滤目录（即第2页到第7页）
            if 1 <= page_idx <= 6:
                filtered_items, removed_count = self.filter_page_content(
                    page_items, self.toc_keywords, 'toc'
                )
                if removed_count > 0:
                    logger.info(f"页面 {page_num}: 删除目录页面 {removed_count} 个项目")
                    pages_filtered += 1
            
            # 后2页过滤声明
            elif page_idx >= max_page_idx - 1:  # 最后两页
                filtered_items, removed_count = self.filter_page_content(
                    page_items, self.disclaimer_keywords, 'disclaimer'
                )
                if removed_count > 0:
                    logger.info(f"页面 {page_num}: 删除声明相关内容 {removed_count} 个项目")
                    pages_filtered += 1
            
            # 中间页面不过滤
            else:
                filtered_items = page_items
                removed_count = 0
            
            filtered_content.extend(filtered_items)
            total_removed += removed_count
        
        stats = {
            'total_items_original': len(content_list),
            'total_items_filtered': len(filtered_content),
            'total_items_removed': total_removed,
            'pages_filtered': pages_filtered
        }
        
        return filtered_content, stats
    
    def process_file(self, json_file_path: Path) -> bool:
        """
        处理单个 JSON 文件
        
        Args:
            json_file_path: JSON 文件路径
            
        Returns:
            是否成功处理
        """
        try:
            # 读取原文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                content_list = json.load(f)
            
            if not isinstance(content_list, list):
                logger.error(f"JSON 文件格式错误，应为列表格式: {json_file_path}")
                return False
            
            original_count = len(content_list)
            
            # 过滤内容
            filtered_content, file_stats = self.filter_content_list(content_list)
            
            # 如果有内容被删除，则保存文件
            if file_stats['total_items_removed'] > 0:
                # 保存过滤后的内容
                with open(json_file_path, 'w', encoding='utf-8') as f:
                    json.dump(filtered_content, f, ensure_ascii=False, indent=2)
                
                # 更新统计
                self.stats['processed_files'] += 1
                self.stats['total_pages_filtered'] += file_stats['pages_filtered']
                self.stats['total_items_removed'] += file_stats['total_items_removed']
                
                logger.info(f"✅ {json_file_path.name}: "
                          f"原始 {original_count} 项 -> "
                          f"过滤后 {len(filtered_content)} 项 "
                          f"(删除 {file_stats['total_items_removed']} 项, "
                          f"{file_stats['pages_filtered']} 页)")
                return True
            else:
                logger.info(f"⏭️  {json_file_path.name}: 未发现需要过滤的内容")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理文件 {json_file_path} 时出错: {e}")
            return False
    
    def process_directory(self, parser_dir: Path) -> Tuple[int, int]:
        """
        处理整个 parser 目录
        
        Args:
            parser_dir: parser 目录路径
            
        Returns:
            (processed_count, total_count): 处理的文件数和总文件数
        """
        self.stats['total_files'] = 0
        self.stats['processed_files'] = 0
        
        # 遍历所有子目录中的 *_content_list.json 文件
        for subdir in sorted(parser_dir.iterdir()):
            if subdir.is_dir():
                # 查找 *_content_list.json 文件
                json_files = list(subdir.glob('*_content_list.json'))
                
                if json_files:
                    # 如果有多个文件，使用第一个
                    if len(json_files) > 1:
                        logger.warning(f"找到多个 JSON 文件，使用第一个: {json_files[0]}")
                    
                    json_file = json_files[0]
                    self.stats['total_files'] += 1
                    
                    logger.info(f"\n📁 处理项目: {subdir.name}")
                    self.process_file(json_file)
        
        return self.stats['processed_files'], self.stats['total_files']


def test_keyword_matching():
    """测试关键词匹配功能"""
    filter_obj = ContentListPageFilter()
    
    # 测试目录关键词
    test_items_toc = [
        {"type": "text", "text_level": 1, "text": "目录"},
        {"type": "text", "text_level": 1, "text": "内容目录"},
        {"type": "text", "text_level": 1, "text": "图表目录"},
        {"type": "text", "text_level": 1, "text": "本期目录"},
        {"type": "text", "text_level": 1, "text": "正文目录"},
        {"type": "text", "text_level": 1, "text": "第一章 概述"},  # 不匹配
        {"type": "text", "text_level": 2, "text": "目录"},  # text_level != 1，不匹配
    ]
    
    # 测试声明关键词
    test_items_disclaimer = [
        {"type": "text", "text_level": 1, "text": "免责声明"},
        {"type": "text", "text_level": 1, "text": "风险提示"},
        {"type": "text", "text_level": 1, "text": "联系方式"},
        {"type": "text", "text_level": 1, "text": "分析师声明"},
        {"type": "text", "text_level": 1, "text": "公司联系方式"},
        {"type": "text", "text_level": 1, "text": "结论"},  # 不匹配
    ]
    
    logger.info("测试目录关键词匹配:")
    for item in test_items_toc:
        is_match = filter_obj.is_target_keyword_title(item, filter_obj.toc_keywords)
        status = "✅ 匹配" if is_match else "❌ 不匹配"
        logger.info(f"  {item['text']:<15} (level={item['text_level']}) -> {status}")
    
    logger.info("\n测试声明关键词匹配:")
    for item in test_items_disclaimer:
        is_match = filter_obj.is_target_keyword_title(item, filter_obj.disclaimer_keywords)
        status = "✅ 匹配" if is_match else "❌ 不匹配"
        logger.info(f"  {item['text']:<15} (level={item['text_level']}) -> {status}")


def main():
    """主函数"""
    logger.info("开始过滤 parser 目录下 Content List 文件的页面内容")
    
    # 测试关键词匹配
    test_keyword_matching()
    logger.info("-" * 60)
    
    # 获取 parser 目录路径
    current_dir = Path.cwd()
    parser_dir = current_dir / "data" / "parser"
    
    if not parser_dir.exists():
        logger.error(f"Parser 目录不存在: {parser_dir}")
        return
    
    # 创建过滤器
    filter_obj = ContentListPageFilter()
    
    # 处理目录
    processed_count, total_count = filter_obj.process_directory(parser_dir)
    
    # 输出统计结果
    logger.info("=" * 80)
    logger.info("Content List 页面过滤完成")
    logger.info("=" * 80)
    logger.info(f"📁 总文件数: {total_count}")
    logger.info(f"✅ 处理文件数: {processed_count}")
    logger.info(f"⏭️  跳过文件数: {total_count - processed_count}")
    logger.info(f"📄 过滤页面数: {filter_obj.stats['total_pages_filtered']}")
    logger.info(f"📋 删除目录页面数: {filter_obj.stats['toc_pages_removed']}")
    logger.info(f"⚠️  删除声明章节数: {filter_obj.stats['disclaimer_sections_removed']}")
    logger.info(f"🗑️  总删除项目数: {filter_obj.stats['total_items_removed']}")
    
    if processed_count > 0:
        logger.info("\n🎉 页面过滤成功完成")
        logger.info("💡 建议重新运行 04_build_vector_index_v2.py 以应用过滤效果")
    else:
        logger.info("\nℹ️  所有文件均无需处理")


if __name__ == "__main__":
    main()