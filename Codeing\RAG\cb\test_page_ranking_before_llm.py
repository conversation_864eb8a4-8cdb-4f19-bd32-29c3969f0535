#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的页面排名分析脚本（针对性检索模式）

功能：
1. 基于TOP1公司匹配，进行针对性检索分析
2. 如果正确答案是TOP1项目：检索15个块
3. 如果正确答案是同公司其他项目：检索8个块
4. 如果正确答案不是该公司：输出"公司错误"
5. 输出检索块的具体内容，便于问题定位

每个问题只检索一个相关数据库，更符合实际检索逻辑。
"""

import json
import logging
import multiprocessing as mp
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed
from modules.vector_retriever import VectorRetriever

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def process_sample_worker(args: Tuple[Dict, int, int]) -> Dict:
    """
    多进程工作函数：处理单个样本
    
    Args:
        args: (sample, sample_index, total_samples) 元组
        
    Returns:
        处理结果字典
    """
    sample, sample_index, total_samples = args
    
    # 为每个进程创建独立的日志记录器
    process_logger = logging.getLogger(f"worker_{mp.current_process().pid}")
    
    try:
        # 为每个进程创建独立的VectorRetriever实例
        retriever = VectorRetriever(db_dir=Path("data/db"))
        
        query = sample['question']
        expected_filename = sample['filename']
        expected_page = sample['page']
        
        process_logger.info(f"🔍 [{sample_index+1}/{total_samples}] 处理: {query[:30]}...")
        
        # 1. 找到目标数据库
        target_info = find_target_database_worker(retriever, query, expected_filename)
        if not target_info:
            return {
                'sample_index': sample_index,
                'status': 'company_error',
                'query': query[:50],
                'expected_filename': expected_filename,
                'expected_page': expected_page
            }
            
        db_name, project_type, k = target_info
        
        # 2. 执行检索
        try:
            chunks = retriever.search_in_database(db_name, query, k=k)
        except Exception as e:
            return {
                'sample_index': sample_index,
                'status': 'retrieval_error',
                'error': str(e),
                'query': query[:50],
                'expected_filename': expected_filename,
                'expected_page': expected_page
            }
            
        # 3. 分析检索结果
        analysis = analyze_chunks_worker(chunks, expected_page, expected_filename)
        
        return {
            'sample_index': sample_index,
            'status': 'success',
            'project_type': project_type,
            'analysis': analysis,
            'query': query[:50],
            'expected_filename': expected_filename,
            'expected_page': expected_page,
            'db_name': db_name,
            'k': k
        }
        
    except Exception as e:
        return {
            'sample_index': sample_index,
            'status': 'error',
            'error': str(e),
            'query': sample.get('question', '')[:50],
            'expected_filename': sample.get('filename', ''),
            'expected_page': sample.get('page', 0)
        }


def find_target_database_worker(retriever: VectorRetriever, query: str, expected_filename: str) -> Optional[Tuple[str, str, int]]:
    """worker版本的find_target_database"""
    try:
        # 1. 获取TOP1数据库
        matched_dbs = retriever.matcher.match_databases(query, max_matches=5)
        if not matched_dbs:
            return None
            
        top1_db_name = matched_dbs[0][0]
        top1_project_name = retriever.matcher.db_mapping.get(top1_db_name)
        
        if not top1_project_name:
            return None
            
        # 2. 获取TOP1公司名
        top1_company = retriever._get_company_from_project_name(top1_project_name)
        if not top1_company:
            return None
        
        # 3. 获取该公司的所有项目
        company_dbs = retriever._get_company_databases(top1_company)
        
        # 4. 检查expected_filename属于哪个项目
        expected_filename_clean = expected_filename.replace('.pdf', '')
        
        # 检查是否是TOP1项目
        if expected_filename_clean == top1_project_name:
            return top1_db_name, "TOP1", 15
            
        # 检查是否是同公司其他项目
        for db_name in company_dbs:
            project_name = retriever.matcher.db_mapping.get(db_name)
            if project_name and expected_filename_clean == project_name:
                return db_name, "OTHER", 8
        
        return None
        
    except Exception:
        return None


def analyze_chunks_worker(chunks: List, expected_page: int, expected_filename: str) -> Dict:
    """worker版本的analyze_chunks"""
    result = {
        'total_chunks': len(chunks),
        'correct_page_chunks': 0,
        'correct_filename_chunks': 0,
        'correct_both_chunks': 0,
        'chunk_details': []
    }
    
    expected_filename_clean = expected_filename.replace('.pdf', '')
    
    for i, chunk in enumerate(chunks):
        # 处理Document对象
        chunk_page = chunk.metadata.get('page_number')
        chunk_project = chunk.metadata.get('project_name', '')
        
        # 从metadata中获取检索分数
        chunk_score = chunk.metadata.get('hybrid_score') or chunk.metadata.get('rrf_score') or chunk.metadata.get('vector_score') or 0.0
        
        is_correct_page = chunk_page == expected_page
        is_correct_filename = chunk_project == expected_filename_clean
        
        if is_correct_page:
            result['correct_page_chunks'] += 1
        if is_correct_filename:
            result['correct_filename_chunks'] += 1
        if is_correct_page and is_correct_filename:
            result['correct_both_chunks'] += 1
    
    return result

class SimplifiedPageAnalyzer:
    def __init__(self):
        """初始化简化页面分析器"""
        self.retriever = VectorRetriever(db_dir=Path("data/db"))
        
        # 统计信息
        self.stats = {
            'total_samples': 0,
            'correct_retrieval': 0,     # 检索到正确页面
            'company_errors': 0,        # 公司匹配错误
            'top1_project_cases': 0,    # TOP1项目的情况
            'other_project_cases': 0,   # 同公司其他项目的情况
            'page_found_in_chunks': 0,  # 在检索块中找到正确页面
            'no_correct_chunks': 0,     # 检索块中没有正确页面
        }

    def find_target_database(self, query: str, expected_filename: str) -> Optional[Tuple[str, str, int]]:
        """
        找到目标检索数据库
        
        Returns:
            Tuple[db_name, project_type, k] or None if company error
            - db_name: 要检索的数据库名称
            - project_type: "TOP1" 或 "OTHER"
            - k: 检索块数量 (15 for TOP1, 8 for OTHER)
        """
        # 1. 获取TOP1数据库
        matched_dbs = self.retriever.matcher.match_databases(query, max_matches=5)
        if not matched_dbs:
            logger.error("❌ 无法匹配到任何数据库")
            return None
            
        top1_db_name = matched_dbs[0][0]
        top1_project_name = self.retriever.matcher.db_mapping.get(top1_db_name)
        
        if not top1_project_name:
            logger.error(f"❌ 无法找到数据库 {top1_db_name} 对应的项目名")
            return None
            
        # 2. 获取TOP1公司名
        top1_company = self.retriever._get_company_from_project_name(top1_project_name)
        if not top1_company:
            logger.error(f"❌ 无法确定项目 '{top1_project_name}' 对应的公司名")
            return None
            
        logger.info(f"🏢 TOP1公司: {top1_company}")
        
        # 3. 获取该公司的所有项目
        company_dbs = self.retriever._get_company_databases(top1_company)
        logger.info(f"📁 公司 '{top1_company}' 共有 {len(company_dbs)} 个项目")
        
        # 4. 检查expected_filename属于哪个项目
        expected_filename_clean = expected_filename.replace('.pdf', '')
        
        # 检查是否是TOP1项目
        if expected_filename_clean == top1_project_name:
            logger.info(f"🎯 目标文件属于TOP1项目: {top1_project_name}")
            return top1_db_name, "TOP1", 15
            
        # 检查是否是同公司其他项目
        for db_name in company_dbs:
            project_name = self.retriever.matcher.db_mapping.get(db_name)
            if project_name and expected_filename_clean == project_name:
                logger.info(f"🎯 目标文件属于同公司其他项目: {project_name}")
                return db_name, "OTHER", 8
                
        # 不是该公司的项目
        logger.warning(f"❌ 目标文件 '{expected_filename}' 不属于公司 '{top1_company}'")
        return None

    def analyze_chunks(self, chunks: List, expected_page: int, expected_filename: str) -> Dict:
        """分析检索到的块"""
        result = {
            'total_chunks': len(chunks),
            'correct_page_chunks': 0,
            'correct_filename_chunks': 0,
            'correct_both_chunks': 0,
            'chunk_details': []
        }
        
        expected_filename_clean = expected_filename.replace('.pdf', '')
        
        for i, chunk in enumerate(chunks):
            # 处理Document对象
            chunk_page = chunk.metadata.get('page_number')
            chunk_project = chunk.metadata.get('project_name', '')
            chunk_content = chunk.page_content[:200] + "..." if len(chunk.page_content) > 200 else chunk.page_content
            
            # 从metadata中获取检索分数
            chunk_score = chunk.metadata.get('hybrid_score') or chunk.metadata.get('rrf_score') or chunk.metadata.get('vector_score') or 0.0
            
            is_correct_page = chunk_page == expected_page
            is_correct_filename = chunk_project == expected_filename_clean
            
            if is_correct_page:
                result['correct_page_chunks'] += 1
            if is_correct_filename:
                result['correct_filename_chunks'] += 1
            if is_correct_page and is_correct_filename:
                result['correct_both_chunks'] += 1
                
            result['chunk_details'].append({
                'rank': i + 1,
                'page': chunk_page,
                'project': chunk_project,
                'score': chunk_score,
                'content_preview': chunk_content,
                'is_correct_page': is_correct_page,
                'is_correct_filename': is_correct_filename,
                'is_perfect_match': is_correct_page and is_correct_filename
            })
            
        return result

    def test_single_sample(self, sample: Dict) -> Dict:
        """测试单个样本"""
        query = sample['question']
        expected_filename = sample['filename']
        expected_page = sample['page']
        
        logger.info(f"\n{'='*50}")
        logger.info(f"🔍 测试问题: {query[:50]}...")
        logger.info(f"📄 预期文档: {expected_filename}")
        logger.info(f"📖 预期页码: {expected_page}")
        
        # 1. 找到目标数据库
        target_info = self.find_target_database(query, expected_filename)
        if not target_info:
            logger.error("❌ 公司匹配错误")
            return {'status': 'company_error'}
            
        db_name, project_type, k = target_info
        logger.info(f"🎯 目标数据库: {db_name}")
        logger.info(f"📊 项目类型: {project_type}")
        logger.info(f"🔢 检索块数: {k}")
        
        # 2. 执行检索
        try:
            chunks = self.retriever.search_in_database(db_name, query, k=k)
            logger.info(f"✅ 检索到 {len(chunks)} 个块")
        except Exception as e:
            logger.error(f"❌ 检索失败: {e}")
            return {'status': 'retrieval_error', 'error': str(e)}
            
        # 3. 分析检索结果
        analysis = self.analyze_chunks(chunks, expected_page, expected_filename)
        
        # 4. 输出详细结果
        logger.info(f"\n📊 检索结果分析:")
        logger.info(f"  总块数: {analysis['total_chunks']}")
        logger.info(f"  正确页码块: {analysis['correct_page_chunks']}")
        logger.info(f"  正确文件块: {analysis['correct_filename_chunks']}")
        logger.info(f"  完美匹配块: {analysis['correct_both_chunks']}")
        
        # 输出前10个块的详细信息
        logger.info(f"\n📋 前10个块详情:")
        for detail in analysis['chunk_details'][:10]:
            status = ""
            if detail['is_perfect_match']:
                status = "✅完美"
            elif detail['is_correct_page']:
                status = "🎯页码"
            elif detail['is_correct_filename']:
                status = "📄文件"
            else:
                status = "❌错误"
                
            logger.info(f"  #{detail['rank']:2d} | 页码:{detail['page']:3d} | 分数:{detail['score']:.3f} | {status}")
            logger.info(f"        项目: {detail['project']}")
            logger.info(f"        内容: {detail['content_preview']}")
            logger.info("")
        
        return {
            'status': 'success',
            'project_type': project_type,
            'analysis': analysis,
            'chunks': chunks
        }

    def run_analysis(self, max_workers: int = None, use_multiprocessing: bool = True):
        """运行完整分析"""
        # 读取训练数据
        train_file = Path("data/train.json")
        if not train_file.exists():
            logger.error(f"❌ 训练文件不存在: {train_file}")
            return
            
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
            
        logger.info(f"📚 加载 {len(train_data)} 个测试样本")
        
        # 确定工作进程数
        if max_workers is None:
            max_workers = min(4, mp.cpu_count(), len(train_data))
        
        if not use_multiprocessing or len(train_data) <= 2:
            logger.info("🔄 使用单进程模式")
            return self._run_analysis_sequential(train_data)
        
        logger.info(f"⚡ 使用多进程模式: {max_workers} 个进程")
        return self._run_analysis_multiprocess(train_data, max_workers)
    
    def _run_analysis_sequential(self, train_data: List[Dict]):
        """单进程分析"""
        for i, sample in enumerate(train_data):
            logger.info(f"\n🚀 开始测试样本 {i+1}/{len(train_data)}")
            
            result = self.test_single_sample(sample)
            self._update_stats(result)
        
        # 输出最终报告
        self.print_final_report()
    
    def _run_analysis_multiprocess(self, train_data: List[Dict], max_workers: int):
        """多进程分析"""
        start_time = time.time()
        
        # 准备任务参数
        tasks = [(sample, i, len(train_data)) for i, sample in enumerate(train_data)]
        
        completed_count = 0
        results = []
        
        try:
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_index = {
                    executor.submit(process_sample_worker, task): task[1] 
                    for task in tasks
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_index):
                    sample_index = future_to_index[future]
                    completed_count += 1
                    
                    try:
                        result = future.result()
                        results.append(result)
                        
                        # 显示进度
                        if completed_count % 10 == 0 or completed_count == len(train_data):
                            progress = (completed_count / len(train_data)) * 100
                            elapsed = time.time() - start_time
                            avg_time = elapsed / completed_count
                            eta = avg_time * (len(train_data) - completed_count)
                            
                            logger.info(f"📈 进度: {completed_count}/{len(train_data)} ({progress:.1f}%) | "
                                      f"已用时: {elapsed:.1f}s | 预计剩余: {eta:.1f}s")
                        
                        # 简要显示结果
                        status = result['status']
                        query_preview = result.get('query', '')[:30]
                        if status == 'success':
                            analysis = result['analysis']
                            perfect = analysis['correct_both_chunks']
                            if perfect > 0:
                                logger.debug(f"✅ [{sample_index+1}] 完美匹配: {query_preview}...")
                            else:
                                logger.debug(f"🔍 [{sample_index+1}] 部分匹配: {query_preview}...")
                        elif status == 'company_error':
                            logger.debug(f"❌ [{sample_index+1}] 公司错误: {query_preview}...")
                        else:
                            logger.debug(f"⚠️ [{sample_index+1}] 处理失败: {query_preview}...")
                        
                    except Exception as e:
                        logger.error(f"❌ 样本 {sample_index+1} 结果处理异常: {e}")
                        results.append({
                            'sample_index': sample_index,
                            'status': 'processing_error',
                            'error': str(e)
                        })
        
        except KeyboardInterrupt:
            logger.warning("⚠️ 用户中断分析")
            return
        except Exception as e:
            logger.error(f"多进程分析异常: {e}")
            return
        
        # 汇总统计结果
        self._aggregate_results(results)
        
        # 计算总用时
        total_time = time.time() - start_time
        logger.info(f"\n🎉 多进程分析完成！")
        logger.info(f"⏱️ 总用时: {total_time:.1f}秒")
        logger.info(f"🚀 平均速度: {len(train_data)/total_time:.1f} 样本/秒")
        
        # 输出最终报告
        self.print_final_report()
    
    def _update_stats(self, result: Dict):
        """更新单个结果的统计"""
        self.stats['total_samples'] += 1
        
        if result['status'] == 'company_error':
            self.stats['company_errors'] += 1
        elif result['status'] == 'success':
            if result['project_type'] == 'TOP1':
                self.stats['top1_project_cases'] += 1
            else:
                self.stats['other_project_cases'] += 1
                
            analysis = result['analysis']
            if analysis['correct_both_chunks'] > 0:
                self.stats['correct_retrieval'] += 1
                self.stats['page_found_in_chunks'] += 1
            elif analysis['correct_page_chunks'] > 0:
                self.stats['page_found_in_chunks'] += 1
            else:
                self.stats['no_correct_chunks'] += 1
    
    def _aggregate_results(self, results: List[Dict]):
        """汇总多进程结果"""
        # 重置统计
        self.stats = {
            'total_samples': 0,
            'correct_retrieval': 0,
            'company_errors': 0,
            'top1_project_cases': 0,
            'other_project_cases': 0,
            'page_found_in_chunks': 0,
            'no_correct_chunks': 0,
        }
        
        # 按sample_index排序
        results.sort(key=lambda x: x['sample_index'])
        
        # 汇总统计
        for result in results:
            self._update_stats(result)
        
        # 显示失败样本统计
        error_samples = [r for r in results if r['status'] not in ['success', 'company_error']]
        if error_samples:
            logger.warning(f"⚠️ 处理失败的样本: {len(error_samples)} 个")
            for error_sample in error_samples[:5]:  # 只显示前5个
                logger.warning(f"   - 样本 {error_sample['sample_index']+1}: {error_sample.get('error', 'Unknown error')}")
            if len(error_samples) > 5:
                logger.warning(f"   ... 还有 {len(error_samples) - 5} 个失败样本")

    def print_final_report(self):
        """输出最终分析报告"""
        logger.info(f"\n{'='*60}")
        logger.info("🏁 简化页面检索分析最终报告")
        logger.info(f"{'='*60}")
        
        total = self.stats['total_samples']
        logger.info(f"📊 总测试样本: {total}")
        logger.info(f"❌ 公司匹配错误: {self.stats['company_errors']} ({self.stats['company_errors']/total*100:.1f}%)")
        logger.info(f"🎯 TOP1项目检索: {self.stats['top1_project_cases']} ({self.stats['top1_project_cases']/total*100:.1f}%)")
        logger.info(f"📁 其他项目检索: {self.stats['other_project_cases']} ({self.stats['other_project_cases']/total*100:.1f}%)")
        
        logger.info(f"\n📈 检索质量分析:")
        logger.info(f"✅ 完美检索（页码+文件）: {self.stats['correct_retrieval']} ({self.stats['correct_retrieval']/total*100:.1f}%)")
        logger.info(f"🎯 找到正确页码: {self.stats['page_found_in_chunks']} ({self.stats['page_found_in_chunks']/total*100:.1f}%)")
        logger.info(f"❌ 未找到正确页码: {self.stats['no_correct_chunks']} ({self.stats['no_correct_chunks']/total*100:.1f}%)")
        
        logger.info(f"\n💡 建议:")
        if self.stats['company_errors'] > 0:
            logger.info("  - 考虑优化公司匹配算法")
        if self.stats['no_correct_chunks'] / total > 0.2:
            logger.info("  - 考虑增加检索块数量")
            logger.info("  - 考虑优化向量索引质量")
        logger.info(f"{'='*60}")


def main():
    """主函数"""
    print("🔍 简化页面检索分析工具")
    print("=" * 50)
    
    # 选择运行模式
    print("\n请选择运行模式：")
    print("1. 单进程模式（详细输出，适合调试）")
    print("2. 多进程模式（快速分析，适合批量测试）")
    print("3. 自定义多进程数")
    
    while True:
        choice = input("\n请输入选择 (1-3): ").strip()
        if choice == '1':
            analyzer = SimplifiedPageAnalyzer()
            analyzer.run_analysis(use_multiprocessing=False)
            break
        elif choice == '2':
            max_cpu = mp.cpu_count()
            max_workers = min(4, max_cpu)
            print(f"使用 {max_workers} 个进程进行分析")
            analyzer = SimplifiedPageAnalyzer()
            analyzer.run_analysis(max_workers=max_workers, use_multiprocessing=True)
            break
        elif choice == '3':
            max_cpu = mp.cpu_count()
            print(f"检测到 {max_cpu} 个CPU核心")
            try:
                max_workers = int(input(f"请输入进程数 (1-{max_cpu}): ").strip())
                if 1 <= max_workers <= max_cpu:
                    if max_workers == 1:
                        analyzer = SimplifiedPageAnalyzer()
                        analyzer.run_analysis(use_multiprocessing=False)
                    else:
                        print(f"使用 {max_workers} 个进程进行分析")
                        analyzer = SimplifiedPageAnalyzer()
                        analyzer.run_analysis(max_workers=max_workers, use_multiprocessing=True)
                    break
                else:
                    print(f"进程数必须在 1-{max_cpu} 之间")
            except ValueError:
                print("请输入有效的数字")
        else:
            print("无效选择，请输入 1、2 或 3")


if __name__ == "__main__":
    main()