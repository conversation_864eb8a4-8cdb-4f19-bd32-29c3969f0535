#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM重排序器

使用LLM对检索结果进行重新排序，评估文本对回答问题的帮助程度

作者: AI Assistant
日期: 2025年
"""

import logging
import time
from typing import List, Dict, Any, Optional
import requests
import json
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class LLMReranker:
    """LLM重排序器"""
    
    def __init__(self):
        """初始化LLM重排序器"""
        self.setup_api_config()
        
    def setup_api_config(self):
        """设置API配置"""
        # 使用与答案生成相同的API配置
        self.api_key = os.getenv('CN_API_KEY')
        self.base_url = os.getenv('CN_BASE_URL', 'https://api.siliconflow.cn/v1')
        self.model = os.getenv('CN_LLM_MODEL', 'moonshotai/Kimi-K2-Instruct')
        
        if not self.api_key:
            logger.error("未找到CN_API_KEY环境变量")
            raise ValueError("CN_API_KEY is required")
        
        logger.info(f"LLM重排序器初始化完成，使用模型: {self.model}")
    
    def rerank_pages(self, query: str, pages: List[Dict[str, Any]], top_k: int = 10) -> List[Dict[str, Any]]:
        """
        对页面进行LLM重排序
        
        Args:
            query: 查询问题
            pages: 页面列表，每个页面包含page_number和content
            top_k: 返回的页面数量
            
        Returns:
            重排序后的页面列表
        """
        if not pages:
            return []
        
        logger.info(f"开始LLM重排序，输入 {len(pages)} 个页面")
        logger.info("使用分页面单独上传模式进行LLM评分...")
        
        # 为每个页面评分（分块上传，逐个评分）
        scored_pages = []
        for i, page in enumerate(pages, 1):
            try:
                logger.debug(f"正在评分第 {i}/{len(pages)} 个页面...")
                score = self.score_page_relevance(query, page['content'])
                scored_pages.append({
                    **page,
                    'llm_score': score,
                    'original_score': page.get('vector_score', 0.0)
                })
                logger.debug(f"页面 {i} LLM评分: {score:.1f}/10")
            except Exception as e:
                logger.error(f"评分页面 {page.get('page_number', 'unknown')} 失败: {e}")
                # 如果评分失败，使用原始分数
                scored_pages.append({
                    **page,
                    'llm_score': 0.0,
                    'original_score': page.get('vector_score', 0.0)
                })
        
        # 计算最终分数：vector_weight = 0.3, llm_weight = 0.7
        # 注意：LLM分数是0-10，向量分数通常是0-1，需要归一化处理
        for page in scored_pages:
            vector_score = page['original_score']  # 0-1范围
            llm_score = page['llm_score']  # 0-10范围
            
            # 将向量分数也转换到0-10范围以匹配数量级
            normalized_vector_score = vector_score * 10.0
            
            final_score = normalized_vector_score * 0.3 + llm_score * 0.7
            page['final_score'] = final_score
        
        # 按最终分数排序
        scored_pages.sort(key=lambda x: x['final_score'], reverse=True)
        
        # 返回top k
        result = scored_pages[:top_k]
        logger.info(f"LLM重排序完成，返回 {len(result)} 个页面")
        
        return result
    
    def score_page_relevance(self, query: str, content: str) -> float:
        """
        使用LLM评估页面内容对回答问题的相关性
        
        Args:
            query: 查询问题
            content: 页面内容
            
        Returns:
            相关性分数 (0-1)
        """
        # 构建评分提示
        prompt = self.build_scoring_prompt(query, content)
        
        try:
            # 调用LLM API
            response = self.call_llm_api(prompt)
            
            # 解析分数
            score = self.parse_score_from_response(response)
            
            return score
            
        except Exception as e:
            logger.error(f"LLM评分失败: {e}")
            return 0.0
    
    def build_scoring_prompt(self, query: str, content: str) -> str:
        """构建评分提示"""
        # 截断内容以避免超过token限制
        max_content_length = 2000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        prompt = f"""请评估以下文本内容对回答给定问题的帮助程度。

问题：{query}

文本内容：
{content}

请从以下几个专业维度进行评估：
1. 直接性：内容是否直接回答了问题的核心要点
2. 完整性：内容是否包含回答问题所需的关键数据、指标或信息
3. 准确性：内容的信息质量、数据可靠性和时效性如何
4. 相关性：内容与问题的主题匹配程度和业务关联性
5. 具体性：内容是否提供了具体的数字、比例、趋势等定量信息

请给出一个0到10之间的整数分数，使用以下11个精细梯度：
- 0分: 完全不相关，内容与问题毫无关联
- 1分: 几乎不相关，仅有极其微弱或模糊的关联
- 2分: 极轻微相关，包含极少的边缘性关联信息
- 3分: 略微相关，涉及问题的很小方面但缺乏实质细节
- 4分: 有些相关，包含部分相关但不全面的信息
- 5分: 中等相关，涉及问题但相关性有限或部分
- 6分: 较为相关，提供相关信息但缺乏深度或特异性
- 7分: 相关，明确关联问题并提供实质但不完全全面的信息
- 8分: 很相关，与问题强烈关联并提供重要信息
- 9分: 高度相关，几乎完全回答问题并提供详细具体信息
- 10分: 完全相关，直接全面回答问题并包含所有必要的具体信息

请只返回整数分数（0-10），不要其他解释。"""

        return prompt
    
    def call_llm_api(self, prompt: str, max_retries: int = 3) -> str:
        """调用LLM API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0,    # 第一阶段温度设置为0确保完全一致性
            'max_tokens': 10     # 只需要返回一个分数
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f'{self.base_url}/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content'].strip()
                    return content
                else:
                    # HTTP错误状态码也需要重试
                    logger.error(f"LLM API调用失败: {response.status_code}, {response.text}")
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避: 1s, 2s, 4s
                        logger.info(f"等待{wait_time}秒后进行第 {attempt + 2} 次重试...")
                        time.sleep(wait_time)
                    continue  # 继续重试
                    
            except Exception as e:
                logger.error(f"LLM API调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避: 1s, 2s, 4s
                    logger.info(f"等待{wait_time}秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(wait_time)
        
        raise Exception("LLM API调用失败")
    
    def parse_score_from_response(self, response: str) -> float:
        """从LLM响应中解析分数"""
        try:
            # 尝试直接解析为浮点数
            score = float(response.strip())
            
            # 确保分数在0-10范围内
            score = max(0.0, min(10.0, score))
            
            return score
            
        except ValueError:
            # 如果无法直接解析，尝试从文本中提取数字
            import re
            numbers = re.findall(r'\b([0-9]|10)\b', response)
            
            if numbers:
                try:
                    score = float(numbers[0])
                    return max(0.0, min(10.0, score))
                except ValueError:
                    pass
            
            logger.warning(f"无法解析LLM响应中的分数: {response}")
            return 0.0
    
    def batch_score(self, query: str, contents: List[str]) -> List[float]:
        """批量评分（可以优化为单次API调用）"""
        scores = []
        for content in contents:
            score = self.score_page_relevance(query, content)
            scores.append(score)
        return scores
    
    def batch_rerank_pages(self, query: str, pages: List[Dict[str, Any]], top_k: int = 1) -> List[Dict[str, Any]]:
        """
        批量重排页面：一次性将多个页面发给LLM进行重排
        
        Args:
            query: 查询问题
            pages: 页面列表，每个页面包含page_number和content
            top_k: 返回的页面数量
            
        Returns:
            重排序后的页面列表（不进行加权计算，直接返回LLM排序结果）
        """
        if not pages:
            return []
        
        if len(pages) == 1:
            return pages[:top_k]
        
        logger.info(f"开始LLM批量重排序，输入 {len(pages)} 个页面")
        
        # 构建批量重排提示
        prompt = self.build_batch_ranking_prompt(query, pages)
        
        try:
            # 调用LLM API进行批量重排
            response = self.call_batch_llm_api(prompt)
            
            # 解析LLM返回的排序结果
            ranked_pages = self.parse_ranking_from_response(response, pages)
            
            # 返回top k结果
            result = ranked_pages[:top_k]
            logger.info(f"LLM批量重排序完成，返回 {len(result)} 个页面")
            
            return result
            
        except Exception as e:
            logger.error(f"LLM批量重排失败: {e}，回退到原始顺序")
            return pages[:top_k]
    
    def build_batch_ranking_prompt(self, query: str, pages: List[Dict[str, Any]]) -> str:
        """构建批量重排提示"""
        # 截断每个页面内容以避免超过token限制
        max_content_per_page = 1500  # 每个页面最多1500字符
        
        # 构建页面内容列表
        pages_text = []
        for i, page in enumerate(pages, 1):
            content = page['content']
            if len(content) > max_content_per_page:
                content = content[:max_content_per_page] + "..."
            
            pages_text.append(f"""页面 {i}:
{content}""")
        
        pages_content = "\n\n" + "="*50 + "\n\n".join(pages_text)
        
        prompt = f"""你是一个RAG（检索增强生成）系统的页面重排器。

你将收到一个查询问题和 {len(pages)} 个相关页面内容。你的任务是根据页面与查询问题的相关性进行排序。

查询问题：{query}

{pages_content}

评估指导原则：

1. 分析推理：
   识别每个页面的关键信息以及它们与查询问题的关联程度。考虑页面是否提供直接答案、部分见解或与查询相关的背景信息。基于页面的具体内容进行评估，避免主观假设。

2. 相关性评估维度：
   - 直接性：页面内容是否直接回答问题的核心要点
   - 完整性：页面是否包含回答问题所需的关键数据、指标或信息
   - 准确性：页面信息的质量、数据可靠性和时效性
   - 特异性：页面内容与问题主题的精确匹配程度
   - 实用性：页面是否提供具体的数字、比例、趋势等定量信息

3. 排序标准：
   - 最高相关性：页面直接全面回答问题，包含所有必要的具体信息
   - 高相关性：页面与问题强烈关联，提供重要且详细的相关信息
   - 中等相关性：页面涉及问题主题，但相关性有限或信息不够全面
   - 低相关性：页面包含部分相关信息，但缺乏实质性细节
   - 最低相关性：页面仅有极其微弱的关联或几乎不相关

4. 客观性要求：
   - 仅根据页面内容相对于查询问题的相关程度进行评估
   - 不要推断页面中未明确说明的信息
   - 避免基于外部知识的主观判断

请按照从最相关到最不相关的顺序排列这些页面，只返回页面编号序列。

格式要求：仅返回用逗号分隔的数字序列
示例：3,1,5,2,4

答案："""

        return prompt
    
    def call_batch_llm_api(self, prompt: str, max_retries: int = 3) -> str:
        """调用LLM API进行批量重排"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.1,  # 低温度确保一致性
            'max_tokens': 200     # 增大max_tokens以支持批量重排的响应
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f'{self.base_url}/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=60  # 增加超时时间，因为批量处理需要更多时间
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content'].strip()
                    return content
                else:
                    logger.warning(f"LLM API返回错误状态码: {response.status_code}")
                    if attempt == max_retries - 1:
                        raise Exception(f"API调用失败: {response.status_code}")
                        
            except requests.RequestException as e:
                logger.warning(f"LLM API调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避
        
        raise Exception("LLM API调用超过最大重试次数")
    
    def parse_ranking_from_response(self, response: str, original_pages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从LLM响应中解析排序结果"""
        try:
            # 查找包含页面编号的排序列表
            import re
            
            # 尝试不同的模式匹配排序结果
            patterns = [
                r'(\d+(?:,\s*\d+)*)',  # 匹配: 3,1,5,2,4 或 3, 1, 5, 2, 4
                r'最相关的页面排序[：:]\s*(\d+(?:,\s*\d+)*)',  # 匹配完整格式
                r'排序[：:]\s*(\d+(?:,\s*\d+)*)',  # 匹配简化格式
            ]
            
            ranking_str = None
            for pattern in patterns:
                match = re.search(pattern, response)
                if match:
                    ranking_str = match.group(1)
                    break
            
            if not ranking_str:
                # 尝试从响应中提取所有数字
                numbers = re.findall(r'\d+', response)
                if numbers and len(numbers) >= len(original_pages):
                    ranking_str = ','.join(numbers[:len(original_pages)])
            
            if not ranking_str:
                logger.warning(f"无法从LLM响应中解析排序: {response}")
                return original_pages
            
            # 解析页面编号列表
            page_indices = [int(x.strip()) for x in ranking_str.split(',')]
            
            # 验证页面编号范围
            valid_indices = [idx for idx in page_indices if 1 <= idx <= len(original_pages)]
            
            if len(valid_indices) != len(original_pages):
                logger.warning(f"排序结果不完整，期望{len(original_pages)}个页面，得到{len(valid_indices)}个")
                # 补充缺失的页面编号
                missing_indices = [i for i in range(1, len(original_pages) + 1) if i not in valid_indices]
                valid_indices.extend(missing_indices)
            
            # 按排序结果重新排列页面
            ranked_pages = []
            for idx in valid_indices[:len(original_pages)]:
                page_index = idx - 1  # 转换为0基索引
                if 0 <= page_index < len(original_pages):
                    ranked_pages.append(original_pages[page_index])
            
            logger.info(f"成功解析LLM排序结果: {ranking_str}")
            return ranked_pages
            
        except Exception as e:
            logger.error(f"解析LLM排序响应失败: {e}")
            return original_pages