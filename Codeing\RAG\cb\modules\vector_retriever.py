#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量索引检索脚本

功能特性：
1. 根据问题匹配向量数据库名称，定位到特定的一个或多个数据库
2. 在匹配的数据库中进行向量检索
3. 支持页面级别的智能检索和LLM重排序
4. 自动扩展到同公司的所有数据库

作者: AI Assistant
日期: 2025年
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dotenv import load_dotenv

# LangChain imports
import faiss
import pickle
import numpy as np
from langchain_openai import OpenAIEmbeddings
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.schema import Document
# BM25Retriever import removed - using pure vector retrieval

# 文本处理 imports
try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

# 导入重排序模块
try:
    from .llm_reranker import LLMReranker
except ImportError:
    from llm_reranker import LLMReranker

# 加载环境变量
load_dotenv()


class NativeFAISS:
    """原生FAISS索引的包装类，模拟langchain FAISS接口"""
    
    def __init__(self, index, texts: List[str], metadatas: List[Dict], embeddings):
        self.index = index
        self.texts = texts  
        self.metadatas = metadatas
        self.embeddings = embeddings
    
    def similarity_search_with_score(self, query: str, k: int = 4):
        """执行相似度搜索并返回分数"""
        # 获取查询向量
        query_embedding = self.embeddings.embed_query(query)
        query_vector = np.array([query_embedding], dtype=np.float32)
        
        # 执行搜索
        scores, indices = self.index.search(query_vector, k)
        
        # 构建结果
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx != -1:  # 有效索引
                doc = Document(
                    page_content=self.texts[idx],
                    metadata=self.metadatas[idx].copy()
                )
                # IndexFlatIP返回内积分数，越大越相似
                results.append((doc, float(score)))
        
        return results
    
    def similarity_search(self, query: str, k: int = 4):
        """执行相似度搜索"""
        results_with_scores = self.similarity_search_with_score(query, k)
        return [doc for doc, score in results_with_scores]


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseMatcher:
    """数据库匹配器，根据问题内容匹配相关的向量数据库"""
    
    def __init__(self, db_dir: Path):
        self.db_dir = db_dir
        self.db_metadata = {}
        self.db_mapping = {}
        self.project_names = []
        self.db_names = []
        self.company_representatives = {}  # 所有项目（不去重）
        self.company_mapping = {}  # 公司名到项目的直接映射
        # 使用稠密向量进行语义匹配
        self.embeddings = None
        self.company_embeddings = None

        self.load_database_metadata()
        self.load_company_mapping()
        self.build_search_index()
    
    def load_database_metadata(self):
        """加载所有数据库的元数据和映射"""
        self.db_metadata = {}
        self.db_mapping = {}

        if not self.db_dir.exists():
            logger.warning(f"数据库目录不存在: {self.db_dir}")
            return

        # 加载数据库映射文件
        mapping_file = self.db_dir / 'db_mapping.json'
        if mapping_file.exists():
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.db_mapping = json.load(f)
                logger.info(f"加载了数据库映射文件，包含 {len(self.db_mapping)} 个映射")
            except Exception as e:
                logger.warning(f"加载数据库映射失败: {e}")

        # 加载各个数据库的元数据
        for db_path in self.db_dir.iterdir():
            if db_path.is_dir():
                metadata_file = db_path / 'metadata.json'
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        # 如果有映射，使用映射中的项目名称
                        if db_path.name in self.db_mapping:
                            metadata['project_name'] = self.db_mapping[db_path.name]
                        self.db_metadata[db_path.name] = metadata
                    except Exception as e:
                        logger.warning(f"加载元数据失败 {metadata_file}: {e}")

        logger.info(f"加载了 {len(self.db_metadata)} 个数据库的元数据")
    
    def load_company_mapping(self):
        """加载公司映射文件"""
        mapping_file = Path("data/company_mapping.json")
        
        if mapping_file.exists():
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.company_mapping = json.load(f)
                logger.info(f"加载了公司映射文件，包含 {len(self.company_mapping)} 个公司")
            except Exception as e:
                logger.warning(f"加载公司映射文件失败: {e}")
                self.company_mapping = {}
        else:
            logger.warning(f"公司映射文件不存在: {mapping_file}")
            self.company_mapping = {}

    def build_search_index(self):
        """构建搜索索引 - 关键词匹配 + 稠密向量检索"""
        if not self.db_metadata:
            return

        # Step 1: 加载所有项目（不去重）
        self._extract_company_representatives()
        
        # Step 2: 构建稠密向量检索器
        self._build_dense_embedding_index()

    def _extract_company_representatives(self):
        """取消去重 - 保留所有项目进行匹配"""
        # 不再去重，保留所有项目
        self.company_representatives = {}
        self.project_names = []
        self.db_names = []
        
        for db_name, metadata in self.db_metadata.items():
            project_name = metadata.get('project_name', db_name)
            company_key = project_name.split('-')[0].strip()
            
            # 为每个项目创建唯一的标识
            unique_key = f"{company_key}_{len(self.project_names)}"
            
            self.company_representatives[unique_key] = {
                'db_name': db_name,
                'project_name': project_name,
                'company_name': company_key
            }
            
            self.project_names.append(project_name)
            self.db_names.append(db_name)
        
        logger.info(f"取消数据库去重：保留所有 {len(self.db_metadata)} 个项目进行匹配")

    def get_keyword_scores(self, query: str) -> List[float]:
        """使用company_mapping.json进行简单关键词匹配：匹配到=1分，没匹配到=0分"""
        if not self.project_names:
            return [0.0] * len(self.project_names)
        
        scores = []
        
        # 对每个项目进行匹配
        for project_name in self.project_names:
            score = 0.0
            
            # 查找该项目属于哪个公司
            for company_name, company_projects in self.company_mapping.items():
                if project_name in company_projects:
                    # 检查查询中是否包含该公司名称
                    if company_name in query:
                        score = 1.0  # 匹配到=1分
                        logger.debug(f"关键词匹配: '{company_name}' 在查询 '{query[:30]}...' 中找到，项目: {project_name[:50]}...")
                    break
            
            scores.append(score)
        
        return scores

    def _build_dense_embedding_index(self):
        """构建稠密向量检索器（语义检索）"""
        try:
            # 初始化embedding模型（使用与主系统相同的模型）
            self.embeddings = OpenAIEmbeddings(
                model=os.getenv('EMBEDDING_MODEL', 'BAAI/bge-m3'),
                openai_api_base=os.getenv('GLOBAL_BASE_URL', 'https://api.siliconflow.cn/v1'),
                openai_api_key=os.getenv('GLOBAL_API_KEY'),
                show_progress_bar=False
            )
            
            # 为每个项目生成嵌入向量
            project_texts = []
            for info in self.company_representatives.values():
                # 只使用项目名称进行语义嵌入，关键词匹配已经处理公司名
                project_name = info['project_name']
                
                # 直接使用项目名称进行语义嵌入
                # 关键词匹配已经处理了公司名，这里专注于项目内容特征
                text = project_name
                project_texts.append(text)
            
            # 分批生成嵌入向量（避免API批量限制）
            self.company_embeddings = []
            batch_size = 50  # BAAI/bge-m3的安全批量大小
            
            for i in range(0, len(project_texts), batch_size):
                batch = project_texts[i:i + batch_size]
                try:
                    batch_embeddings = self.embeddings.embed_documents(batch)
                    self.company_embeddings.extend(batch_embeddings)
                    logger.debug(f"完成批次 {i//batch_size + 1}/{(len(project_texts)-1)//batch_size + 1}: {len(batch)} 个项目")
                    
                    # 批次间休息
                    if i + batch_size < len(project_texts):
                        import time
                        time.sleep(1)  # 1秒间隔
                        
                except Exception as e:
                    logger.warning(f"批次嵌入失败 (批次 {i//batch_size + 1}): {e}")
                    # 如果批次失败，尝试更小的批次
                    if batch_size > 10:
                        smaller_batch_size = batch_size // 2
                        logger.info(f"尝试更小批次大小: {smaller_batch_size}")
                        for j in range(i, min(i + batch_size, len(project_texts)), smaller_batch_size):
                            smaller_batch = project_texts[j:j + smaller_batch_size]
                            try:
                                smaller_embeddings = self.embeddings.embed_documents(smaller_batch)
                                self.company_embeddings.extend(smaller_embeddings)
                                time.sleep(1)
                            except Exception as e2:
                                logger.error(f"小批次也失败: {e2}")
                                # 如果还是失败，用零向量填充
                                self.company_embeddings.extend([[0.0] * 768] * len(smaller_batch))
                    else:
                        # 如果批次已经很小还是失败，用零向量填充
                        self.company_embeddings.extend([[0.0] * 768] * len(batch))
            
            logger.info(f"稠密向量索引构建完成，包含 {len(self.company_embeddings)} 个项目向量")
            
        except Exception as e:
            logger.warning(f"稠密向量索引构建失败: {e}")
            self.embeddings = None
            self.company_embeddings = None


    

    


    def get_semantic_scores(self, query: str) -> List[float]:
        """使用稠密向量计算语义相似度分数"""
        if not self.embeddings or not self.company_embeddings:
            return [0.0] * len(self.project_names)

        try:
            # 将查询转换为稠密向量
            query_embedding = self.embeddings.embed_query(query)
            
            # 计算与每个项目的余弦相似度
            similarities = []
            for project_embedding in self.company_embeddings:
                # 计算余弦相似度
                dot_product = sum(q * p for q, p in zip(query_embedding, project_embedding))
                query_norm = sum(q * q for q in query_embedding) ** 0.5
                project_norm = sum(p * p for p in project_embedding) ** 0.5
                
                if query_norm > 0 and project_norm > 0:
                    similarity = dot_product / (query_norm * project_norm)
                else:
                    similarity = 0.0
                
                similarities.append(max(0.0, similarity))  # 确保非负
            
            return similarities
        except Exception as e:
            logger.warning(f"稠密向量相似度计算失败: {e}")
            return [0.0] * len(self.project_names)
    
    def match_databases(self, query: str, max_matches: int = 3,
                       keyword_weight: float = 0.3, semantic_weight: float = 0.7) -> List[Tuple[str, float]]:
        """使用关键词匹配和语义相似度混合检索匹配数据库"""
        if not self.project_names:
            return []

        # 获取关键词匹配分数
        keyword_scores = self.get_keyword_scores(query)

        # 获取语义相似度分数（稠密检索）
        semantic_scores = self.get_semantic_scores(query)

        # 归一化分数
        if max(keyword_scores) > 0:
            keyword_scores = [score / max(keyword_scores) for score in keyword_scores]

        if max(semantic_scores) > 0:
            semantic_scores = [score / max(semantic_scores) for score in semantic_scores]

        # 计算混合分数
        matched_dbs = []
        for i, (db_name, project_name) in enumerate(zip(self.db_names, self.project_names)):
            # 混合分数：关键词匹配0.4 + 语义搜索0.6
            hybrid_score = (keyword_weight * keyword_scores[i] +
                           semantic_weight * semantic_scores[i])

            if hybrid_score > 0:
                matched_dbs.append((db_name, hybrid_score))
                logger.debug(f"匹配: {project_name[:50]}... "
                           f"(关键词: {keyword_scores[i]:.3f}, "
                           f"语义: {semantic_scores[i]:.3f}, "
                           f"混合: {hybrid_score:.3f})")

        # 按分数排序
        matched_dbs.sort(key=lambda x: x[1], reverse=True)

        # 如果没有匹配项，返回前几个数据库
        if not matched_dbs:
            matched_dbs = [(db_name, 0.1) for db_name in self.db_names[:max_matches]]

        # 记录匹配结果
        logger.info(f"数据库匹配结果 (前{min(max_matches, len(matched_dbs))}个):")
        for i, (db_name, score) in enumerate(matched_dbs[:max_matches], 1):
            project_name = self.db_metadata[db_name].get('project_name', db_name)
            logger.info(f"  {i}. {project_name[:60]}... (分数: {score:.3f})")

        return matched_dbs[:max_matches]

class VectorRetriever:
    """向量检索器"""
    
    def __init__(self, db_dir: Path):
        self.db_dir = db_dir
        self.setup_embedding_model()
        self.matcher = DatabaseMatcher(db_dir)
        self.loaded_databases = {}

        # 初始化LLM重排序器
        try:
            self.llm_reranker = LLMReranker()
            logger.info("LLM重排序器已启用")
        except Exception as e:
            logger.warning(f"LLM重排序器初始化失败: {e}")
            self.llm_reranker = None

        # 构建公司到数据库的映射
        self.company_db_mapping = self._build_company_db_mapping()
        logger.info(f"构建了 {len(self.company_db_mapping)} 个公司的数据库映射")
    
    def setup_embedding_model(self):
        """设置嵌入模型（与构建时使用相同的模型）"""
        embedding_model = os.getenv('EMBEDDING_MODEL', 'BAAI/bge-m3')
        cn_base_url = os.getenv('GLOBAL_BASE_URL', 'https://api.siliconflow.cn/v1')
        cn_api_key = os.getenv('GLOBAL_API_KEY')
        
        if not cn_api_key:
            logger.warning("未设置 GLOBAL_API_KEY，尝试使用本地嵌入模型")
        
        try:
            if cn_api_key:
                self.embeddings = OpenAIEmbeddings(
                    model=embedding_model,
                    openai_api_base=cn_base_url,
                    openai_api_key=cn_api_key
                )
                logger.info(f"成功初始化嵌入模型: {embedding_model}")
            else:
                raise ValueError("No API key")
        except Exception as e:
            logger.warning(f"初始化在线嵌入模型失败: {e}")
            logger.info("使用本地 HuggingFace 模型...")
            self.embeddings = HuggingFaceEmbeddings(
                model_name="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            )
            logger.info("成功初始化本地嵌入模型")
    
    def load_database(self, db_name: str) -> Optional[NativeFAISS]:
        """加载指定的向量数据库"""
        if db_name in self.loaded_databases:
            return self.loaded_databases[db_name]
        
        db_path = self.db_dir / db_name
        if not db_path.exists():
            logger.error(f"数据库不存在: {db_path}")
            return None
        
        try:
            # 尝试加载原生FAISS格式
            faiss_file = db_path / "index.faiss"
            texts_file = db_path / "texts.pkl"
            metadatas_file = db_path / "metadatas.pkl"
            
            if all(f.exists() for f in [faiss_file, texts_file, metadatas_file]):
                # 加载原生FAISS格式
                index = faiss.read_index(str(faiss_file))
                
                with open(texts_file, 'rb') as f:
                    texts = pickle.load(f)
                
                with open(metadatas_file, 'rb') as f:
                    metadatas = pickle.load(f)
                
                vector_store = NativeFAISS(index, texts, metadatas, self.embeddings)
                self.loaded_databases[db_name] = vector_store
                logger.info(f"成功加载原生FAISS数据库: {db_name}, 索引类型: {type(index).__name__}")
                return vector_store
            else:
                # 尝试加载旧的langchain FAISS格式（兼容性）
                try:
                    from langchain_community.vectorstores import FAISS as LangChainFAISS
                    vector_store = LangChainFAISS.load_local(str(db_path), self.embeddings, allow_dangerous_deserialization=True)
                    self.loaded_databases[db_name] = vector_store
                    logger.info(f"成功加载LangChain FAISS数据库: {db_name}")
                    return vector_store
                except Exception as legacy_e:
                    logger.error(f"加载LangChain FAISS格式也失败: {legacy_e}")
                    return None
                    
        except Exception as e:
            logger.error(f"加载数据库失败 {db_name}: {e}")
            return None
    
    def search_in_database(self, db_name: str, query: str, k: int = 5) -> List[Document]:
        """在指定数据库中进行纯向量检索"""
        vector_store = self.load_database(db_name)
        if not vector_store:
            return []

        try:
            # 执行纯向量相似性搜索，并获取分数
            results_with_scores = vector_store.similarity_search_with_score(query, k=k)
            results = []
            for doc, score in results_with_scores:
                # 根据索引类型处理分数
                if isinstance(vector_store, NativeFAISS):
                    # IndexFlatIP返回内积分数，越大越相似，直接使用
                    similarity_score = max(0.0, float(score))  # 确保非负
                else:
                    # 旧格式，假设是距离（越小越相似）
                    similarity_score = 1 / (1 + score)  # 转换FAISS距离为相似度
                doc.metadata['vector_score'] = similarity_score
                results.append(doc)

            logger.info(f"在数据库 {db_name} 中找到 {len(results)} 个结果")
            return results
        except Exception as e:
            logger.error(f"搜索失败 {db_name}: {e}")
            return []



    def get_document_id(self, doc: Document) -> str:
        """生成文档唯一标识"""
        metadata = doc.metadata
        return f"{metadata.get('project_name', '')}_{metadata.get('page_number', 0)}_{metadata.get('chunk_index', 0)}"



    def aggregate_chunks_by_page(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        按页面聚合块，并扩展为完整页面内容
        
        Args:
            chunks: 块列表
            
        Returns:
            聚合后的页面列表
        """
        logger.info(f"开始按页面聚合 {len(chunks)} 个块")
        
        # 按数据库和页码分组
        page_groups = {}
        for chunk in chunks:
            metadata = chunk['metadata']
            database = chunk['database']
            page_number = metadata.get('page_number', 0)
            project_name = metadata.get('project_name', 'unknown')
            
            # 创建页面唯一标识
            page_key = f"{database}_{project_name}_{page_number}"
            
            if page_key not in page_groups:
                page_groups[page_key] = {
                    'database': database,
                    'project_name': project_name,
                    'page_number': page_number,
                    'chunks': [],
                    'max_match_score': 0.0
                }
            
            page_groups[page_key]['chunks'].append(chunk)
            page_groups[page_key]['max_match_score'] = max(
                page_groups[page_key]['max_match_score'], 
                chunk['match_score']
            )
        
        # 为每个页面扩展完整内容
        pages = []
        for page_key, page_info in page_groups.items():
            expanded_content = self.expand_page_content(
                page_info['database'], 
                page_info['page_number'],
                page_info['project_name']
            )
            
            pages.append({
                'database': page_info['database'],
                'project_name': page_info['project_name'], 
                'page_number': page_info['page_number'],
                'content': expanded_content,
                'vector_score': page_info['max_match_score'],
                'chunk_count': len(page_info['chunks']),
                'original_chunks': page_info['chunks']
            })
        
        logger.info(f"聚合得到 {len(pages)} 个页面")
        return pages
    
    def expand_page_content(self, db_name: str, page_number: int, project_name: str) -> str:
        """
        扩展获取指定页面的完整内容（修复版本）
        
        Args:
            db_name: 数据库名称
            page_number: 页码
            project_name: 项目名称
            
        Returns:
            页面完整内容
        """
        vector_store = self.load_database(db_name)
        if not vector_store:
            logger.warning(f"无法加载数据库 {db_name}")
            return ""
        
        try:
            same_page_chunks = []
            
            # 方法1: 尝试通过FAISS内部结构直接访问所有文档
            try:
                if hasattr(vector_store, 'docstore') and hasattr(vector_store, 'index_to_docstore_id'):
                    logger.debug(f"使用方法1: 直接访问FAISS docstore")
                    # 遍历所有文档ID
                    for idx, doc_id in vector_store.index_to_docstore_id.items():
                        try:
                            doc = vector_store.docstore.search(doc_id)
                            if doc and hasattr(doc, 'metadata'):
                                chunk_page = doc.metadata.get('page_number', 0)
                                chunk_project = doc.metadata.get('project_name', '')
                                
                                if (chunk_page == page_number and 
                                    chunk_project == project_name):
                                    same_page_chunks.append(doc)
                        except Exception as e:
                            logger.debug(f"访问文档 {doc_id} 失败: {e}")
                            continue
                            
                    logger.debug(f"方法1成功: 找到 {len(same_page_chunks)} 个同页面块")
                
            except Exception as e:
                logger.debug(f"方法1失败: {e}")
                same_page_chunks = []
            
            # 方法2: 如果方法1失败，使用大K值检索然后过滤
            if not same_page_chunks:
                try:
                    logger.debug(f"使用方法2: 大K值检索+过滤")
                    # 使用一个通用查询获取尽可能多的文档
                    all_candidates = vector_store.similarity_search(
                        project_name if project_name else "内容", k=1000
                    )
                    
                    # 过滤出同一页面和项目的块
                    for chunk in all_candidates:
                        chunk_page = chunk.metadata.get('page_number', 0)
                        chunk_project = chunk.metadata.get('project_name', '')
                        
                        if (chunk_page == page_number and 
                            chunk_project == project_name):
                            same_page_chunks.append(chunk)
                    
                    logger.debug(f"方法2成功: 找到 {len(same_page_chunks)} 个同页面块")
                    
                except Exception as e:
                    logger.debug(f"方法2失败: {e}")
                    same_page_chunks = []
            
            # 方法3: 备选方案 - 使用多个查询策略
            if not same_page_chunks:
                try:
                    logger.debug(f"使用方法3: 多查询策略")
                    # 尝试多个不同的查询
                    queries = [
                        f"{project_name} 第{page_number}页" if project_name else f"第{page_number}页",
                        f"页码{page_number}",
                        project_name if project_name else "文档内容",
                        ""  # 空查询获取随机文档
                    ]
                    
                    all_candidates = []
                    for query in queries:
                        try:
                            candidates = vector_store.similarity_search(query, k=200)
                            all_candidates.extend(candidates)
                        except:
                            continue
                    
                    # 去重并过滤
                    seen_ids = set()
                    for chunk in all_candidates:
                        chunk_id = id(chunk)
                        if chunk_id in seen_ids:
                            continue
                        seen_ids.add(chunk_id)
                        
                        chunk_page = chunk.metadata.get('page_number', 0)
                        chunk_project = chunk.metadata.get('project_name', '')
                        
                        if (chunk_page == page_number and 
                            chunk_project == project_name):
                            same_page_chunks.append(chunk)
                    
                    logger.debug(f"方法3成功: 找到 {len(same_page_chunks)} 个同页面块")
                    
                except Exception as e:
                    logger.debug(f"方法3失败: {e}")
            
            # 如果找到了同页面的块，进行排序和合并
            if same_page_chunks:
                # 按chunk_index排序
                same_page_chunks.sort(key=lambda x: x.metadata.get('chunk_index', 0))
                
                # 合并内容
                page_content = '\n\n'.join([chunk.page_content for chunk in same_page_chunks])
                
                logger.info(f"✅ 页面扩展成功: 项目={project_name}, 页码={page_number}, "
                           f"块数={len(same_page_chunks)}, 内容长度={len(page_content)}字符")
                return page_content
            else:
                logger.warning(f"❌ 页面扩展失败: 未找到页面内容 - 项目={project_name}, 页码={page_number}")
                return ""
            
        except Exception as e:
            logger.error(f"页面扩展出错: {db_name} 页面{page_number} - {e}")
            return ""
    
    def _get_company_from_project_name(self, project_name: str) -> Optional[str]:
        """从项目名称中查找对应的公司名（使用company_mapping.json）"""
        try:
            # 在company_mapping.json中查找该项目属于哪个公司
            for company_name, projects in self.matcher.company_mapping.items():
                if project_name in projects:
                    return company_name
            return None
                
        except Exception as e:
            logger.warning(f"从项目名查找公司失败 {project_name}: {e}")
            return None
    
    def _get_company_databases(self, company_name: str) -> List[str]:
        """根据公司名获取该公司的所有数据库ID"""
        try:
            # 获取该公司的所有项目名
            company_projects = self.matcher.company_mapping.get(company_name, [])
            
            # 将项目名转换为数据库ID
            db_ids = []
            for project_name in company_projects:
                # 在db_mapping.json中查找项目名对应的数据库ID
                for db_id, mapped_project_name in self.matcher.db_mapping.items():
                    if mapped_project_name == project_name:
                        db_ids.append(db_id)
                        break
            
            return db_ids
                
        except Exception as e:
            logger.warning(f"获取公司数据库失败 {company_name}: {e}")
            return []
    
    def retrieve_with_page_expansion(self, query: str) -> Optional[Dict[str, Any]]:
        """
        基于TOP1公司聚焦检索：获取TOP1公司的所有数据库进行检索 → 排序取top20 → 按页面聚合 → LLM重排 → 返回top 1页面
        
        Args:
            query: 查询文本
            use_hybrid: 是否使用混合检索（BM25+向量）
            
        Returns:
            最佳匹配的页面信息，包含页码和文件名
        """
        logger.info(f"开始基于TOP1公司聚焦检索: {query}")
        
        # 1. 匹配数据库获取TOP1
        matched_dbs = self.matcher.match_databases(query, max_matches=5)
        logger.info(f"精准匹配到 {len(matched_dbs)} 个数据库")
        
        if not matched_dbs:
            logger.warning("未匹配到任何数据库")
            return None
        
        # 2. 获取TOP1公司名
        top1_db_name = matched_dbs[0][0]
        top1_project_name = self.matcher.db_mapping.get(top1_db_name)
        
        if not top1_project_name:
            logger.warning(f"无法找到数据库 {top1_db_name} 对应的项目名")
            return None
            
        top1_company = self._get_company_from_project_name(top1_project_name)
        
        if not top1_company:
            logger.warning(f"无法确定项目 '{top1_project_name}' 对应的公司名")
            return None
        
        logger.info(f"锁定TOP1公司: {top1_company}")
        
        # 3. 获取该公司的所有数据库并检索
        all_company_dbs = self._get_company_databases(top1_company)
        logger.info(f"'{top1_company}' 共有 {len(all_company_dbs)} 个数据库")
        
        all_candidate_chunks = []
        
        # 4. 从该公司的所有数据库中检索块（调整检索数量）
        for i, db_name in enumerate(all_company_dbs):
            # TOP1数据库检索更多块，其他数据库检索标准块数
            k = 6 if db_name == top1_db_name else 5
            logger.info(f"在数据库 {db_name} 中检索 {k} 个块")
            chunks = self.search_in_database(db_name, query, k=k)
            
            # 记录块信息
            for chunk in chunks:
                chunk_score = (chunk.metadata.get('vector_score') or 
                              getattr(chunk, 'score', None) or 
                              chunk.metadata.get('score'))
                
                if chunk_score is None:
                    chunk_score = 0.5
                    logger.warning(f"未找到向量分数，使用默认值0.5 - 数据库:{db_name}")
                
                all_candidate_chunks.append({
                    'database': db_name,
                    'chunk_score': chunk_score,
                    'content': chunk.page_content,
                    'metadata': chunk.metadata.copy(),
                    'original_chunk': chunk
                })
        
        total_chunks = len(all_candidate_chunks)
        logger.info(f"聚焦 '{top1_company}' 公司，总共收集到 {total_chunks} 个候选块")
        
        # 4. 对所有候选块按相似度分数排序，取top20
        all_candidate_chunks.sort(key=lambda x: x['chunk_score'], reverse=True)
        
        # 检查分数质量，决定最终取多少个块
        if all_candidate_chunks:
            max_score = all_candidate_chunks[0]['chunk_score']
            avg_score = sum(chunk['chunk_score'] for chunk in all_candidate_chunks) / len(all_candidate_chunks)
            
            # 根据分数质量决定取块数量（不过滤噪音页面，保持混合检索的高召回率）
            if max_score < 0.5 and avg_score < 0.3:
                # 分数很低，只取前10个最好的块
                selected_chunks = all_candidate_chunks[:10]
                logger.warning(f"检索分数很低(最高:{max_score:.3f}, 平均:{avg_score:.3f})，选择前10个块")
            elif max_score < 0.7 and avg_score < 0.5:
                # 分数中等，取前15个块
                selected_chunks = all_candidate_chunks[:15]
                logger.info(f"检索分数中等(最高:{max_score:.3f}, 平均:{avg_score:.3f})，选择前15个块")
            else:
                # 分数正常，取前20个块
                selected_chunks = all_candidate_chunks[:20]
                logger.info(f"检索分数正常(最高:{max_score:.3f}, 平均:{avg_score:.3f})，选择前20个块")
        else:
            logger.warning("未收集到任何候选块")
            return None
        
        # 重新格式化选中的块
        all_chunks = []
        for chunk_data in selected_chunks:
            all_chunks.append({
                'database': chunk_data['database'],
                'match_score': chunk_data['chunk_score'],  # 使用实际的块相似度分数
                'content': chunk_data['content'],
                'metadata': chunk_data['metadata'],
                'original_chunk': chunk_data['original_chunk']
            })
        
        logger.info(f"最终选择 {len(all_chunks)} 个高质量块进行页面聚合")
        
        if not all_chunks:
            logger.warning("未检索到任何块")
            return None
        
        # 5. 按页面聚合并扩展内容
        pages = self.aggregate_chunks_by_page(all_chunks)
        if not pages:
            logger.warning("页面聚合后无结果")
            return None
        
        logger.info(f"聚合得到 {len(pages)} 个页面")
        
        # 6. LLM重排序页面：两层重排机制
        if self.llm_reranker and len(pages) > 1:
            logger.info(f"开始两阶段LLM重排序，共 {len(pages)} 个页面...")
            
            # 第一阶段：单页面重排 + 权重合成，选出候选页面
            logger.info("第一阶段：单页面重排 + 权重合成...")
            
            # 检查向量评分质量，决定重排策略
            max_vector_score = max(page['vector_score'] for page in pages)
            avg_vector_score = sum(page['vector_score'] for page in pages) / len(pages)
            
            # 根据向量质量决定第一阶段候选页面数量
            if max_vector_score < 0.5 and avg_vector_score < 0.3:
                first_stage_candidates = min(8, len(pages))  # 低质量时多考虑一些候选
                logger.warning(f"检索评分较低(最高:{max_vector_score:.3f}, 平均:{avg_vector_score:.3f})，"
                             f"第一阶段考虑前{first_stage_candidates}个页面")
            else:
                first_stage_candidates = min(20, len(pages))  # 正常质量时考虑20个候选页面
                logger.info(f"检索评分正常(最高:{max_vector_score:.3f}, 平均:{avg_vector_score:.3f})，"
                           f"第一阶段考虑前{first_stage_candidates}个页面")
            
            # 准备第一阶段数据：转换为LLM重排器期望的格式
            first_stage_pages = []
            for page in pages[:first_stage_candidates]:
                first_stage_pages.append({
                    'page_number': page['page_number'],
                    'content': page['content'],
                    'vector_score': page['vector_score'],
                    'project_name': page['project_name'],
                    'database': page['database'],
                    'chunk_count': page['chunk_count']
                })
            
            # 执行第一阶段：单页面LLM评分 + 权重合成，取top5
            logger.info(f"执行第一阶段单页面重排，处理{len(first_stage_pages)}个页面...")
            first_stage_ranked = self.llm_reranker.rerank_pages(query, first_stage_pages, top_k=5)
            logger.info(f"第一阶段完成，选出{len(first_stage_ranked)}个页面进入第二阶段")
            
            # 第二阶段：批量重排，全局比较
            logger.info("第二阶段：批量重排全局比较...")
            
            # 为第二阶段准备数据格式  
            llm_pages = []
            for i, page in enumerate(first_stage_ranked):
                llm_pages.append({
                    'page_number': i + 1,  # LLM重排器期望的临时页码
                    'content': page['content'],
                    'vector_score': page['vector_score'],
                    'final_score': page.get('final_score', 0.0),  # 第一阶段的加权分数
                    'original_data': page  # 保存原始页面数据
                })
            
            # 执行第二阶段：批量重排
            reranked_pages = self.llm_reranker.batch_rerank_pages(query, llm_pages, top_k=1)
            logger.info(f"LLM批量重排序完成，处理了 {len(llm_pages)} 个页面，返回top 1最佳页面")
            
            if reranked_pages:
                # 返回top 1页面
                best_page = reranked_pages[0]
                original_page_data = best_page.get('original_data', {})
                
                result = {
                    'database': original_page_data.get('database', ''),
                    'project_name': original_page_data.get('project_name', ''),
                    'page_number': original_page_data.get('page_number', 0),
                    'content': best_page.get('content', ''),
                    'final_score': original_page_data.get('vector_score', 0.0),  # 不进行加权计算，使用原始向量分数
                    'llm_score': 10.0,  # 批量重排的结果，直接标记为满分
                    'vector_score': original_page_data.get('vector_score', 0.0),
                    'chunk_count': original_page_data.get('chunk_count', 0),
                    'rerank_method': 'batch_llm_rerank'  # 标识使用了批量重排
                }
                
                # 计算用于显示的标准化向量分数
                normalized_vector_score = result['vector_score'] * 10.0
                logger.info(f"✅ 返回最佳页面(批量重排): 项目={result['project_name']}, 页码={result['page_number']}, "
                           f"向量分数={result['vector_score']:.3f}→{normalized_vector_score:.1f} (批量LLM重排)")
                
                return result
        else:
            # 如果没有LLM重排序器或只有一个页面，按向量分数排序
            logger.info("无LLM重排序器，按向量分数排序")
            pages.sort(key=lambda x: x['vector_score'], reverse=True)
            
            best_page = pages[0]
            result = {
                'database': best_page['database'],
                'project_name': best_page['project_name'],
                'page_number': best_page['page_number'],
                'content': best_page['content'],
                'final_score': best_page['vector_score'],
                'llm_score': 0.0,
                'vector_score': best_page['vector_score'],
                'chunk_count': best_page['chunk_count']
            }
            
            # 计算用于显示的标准化向量分数
            normalized_vector_score = result['vector_score'] * 10.0
            logger.info(f"✅ 返回最佳页面（无重排）: 项目={result['project_name']}, "
                       f"页码={result['page_number']}, 向量分数={result['vector_score']:.3f}→{normalized_vector_score:.1f}")
            
            return result
        
        logger.warning("未能返回任何页面结果")
        return None
    
    def retrieve(self, query: str) -> Optional[Dict[str, Any]]:
        """
        执行页面级别的智能检索

        Args:
            query: 查询文本

        Returns:
            最佳匹配的页面信息，包含项目名称、页码、内容等
        """
        logger.info(f"开始检索查询: {query}")
        return self.retrieve_with_page_expansion(query)
    
    def format_results(self, result: Optional[Dict[str, Any]]) -> str:
        """格式化检索结果（新的单页面格式）"""
        if not result:
            return "未找到相关结果。"
        
        formatted = []
        formatted.append(f"=== 最佳匹配页面 ===")
        formatted.append(f"项目: {result.get('project_name', 'N/A')}")
        formatted.append(f"页码: {result.get('page_number', 'N/A')}")
        formatted.append(f"数据库: {result.get('database', 'N/A')}")
        formatted.append(f"最终分数: {result.get('final_score', 0.0):.2f}")
        formatted.append(f"LLM分数: {result.get('llm_score', 0.0):.1f}/10")
        vector_score = result.get('vector_score', 0.0)
        normalized_vector_score = vector_score * 10.0
        formatted.append(f"向量分数: {vector_score:.3f}→{normalized_vector_score:.1f}/10")
        formatted.append(f"块数量: {result.get('chunk_count', 0)}")
        formatted.append(f"内容长度: {len(result.get('content', ''))} 字符")
        formatted.append("\n页面内容:")
        
        content = result.get('content', '')
        if len(content) > 1000:
            formatted.append(content[:1000] + "...")
        else:
            formatted.append(content)
        
        return "\n".join(formatted)

    def _build_company_db_mapping(self) -> Dict[str, List[str]]:
        """
        构建公司到数据库的映射

        Returns:
            公司名称到数据库列表的映射
        """
        company_mapping = {}

        # 遍历所有数据库
        for db_name, project_name in self.matcher.db_mapping.items():
            # 提取公司名称（通常是项目名称的第一部分）
            company_name = self._extract_company_name(project_name)

            if company_name not in company_mapping:
                company_mapping[company_name] = []

            company_mapping[company_name].append(db_name)

        # 按数据库数量排序，方便调试
        for company, dbs in company_mapping.items():
            company_mapping[company] = sorted(dbs)
            logger.debug(f"公司 {company}: {len(dbs)} 个数据库")

        return company_mapping

    def _extract_company_name(self, project_name: str) -> str:
        """
        从项目名称中提取公司名称

        Args:
            project_name: 项目名称，如 "伊利股份-公司研究报告-平台化的乳企龙头-25071638页"

        Returns:
            公司名称，如 "伊利股份"
        """
        # 常见的分隔符
        separators = ['-', '—', '_', '：', ':', '（', '(']

        company_name = project_name
        for sep in separators:
            if sep in company_name:
                company_name = company_name.split(sep)[0].strip()
                break

        return company_name



def main():
    """主函数 - 交互式检索"""
    # 配置路径
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    
    if not db_dir.exists():
        logger.error(f"向量数据库目录不存在: {db_dir}")
        logger.error("请先运行 build_vector_index.py 构建向量索引")
        return
    
    # 创建检索器
    try:
        retriever = VectorRetriever(db_dir)
    except Exception as e:
        logger.error(f"初始化检索器失败: {e}")
        return
    
    logger.info("向量检索器已启动，输入 'quit' 退出")
    logger.info("=" * 50)
    
    while True:
        try:
            query = input("\n请输入检索查询: ").strip()
            
            if not query:
                continue
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            # 执行检索
            result = retriever.retrieve(query)
            
            # 显示结果
            formatted_result = retriever.format_results(result)
            print(formatted_result)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"检索过程中出现错误: {e}")
    
    logger.info("检索器已退出")

if __name__ == "__main__":
    main()