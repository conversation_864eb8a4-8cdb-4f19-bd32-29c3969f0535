#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片描述合并脚本

将 full.md 文件中的图片描述信息合并到 *_content_list.json 文件中
通过匹配 img_path 将图片描述添加到对应的图片条目中
"""

import os
import json
import re
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def extract_image_descriptions_from_md(md_file_path: str) -> Dict[str, str]:
    """
    从 MD 文件中提取图片描述信息
    
    Args:
        md_file_path: MD 文件路径
        
    Returns:
        字典：{图片路径: 图片描述}
    """
    image_descriptions = {}
    path_duplicates = {}  # 跟踪重复路径
    
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 匹配图片语法：![描述](图片路径)
        # 使用非贪婪匹配确保正确提取描述文本
        pattern = r'!\[([^\]]+)\]\(([^)]+)\)'
        matches = re.findall(pattern, content)
        
        for description, img_path in matches:
            # 清理图片路径，移除可能的相对路径前缀
            clean_path = img_path.strip()
            if clean_path.startswith('./'):
                clean_path = clean_path[2:]
            
            # 检测重复路径
            if clean_path in image_descriptions:
                if clean_path not in path_duplicates:
                    path_duplicates[clean_path] = [image_descriptions[clean_path]]
                path_duplicates[clean_path].append(description.strip())
                logger.warning(f"⚠️ 发现重复图片路径: {clean_path}")
            else:
                image_descriptions[clean_path] = description.strip()
                logger.debug(f"提取图片描述: {clean_path} -> {description[:50]}...")
        
        # 报告重复路径统计
        if path_duplicates:
            logger.warning(f"⚠️ 发现 {len(path_duplicates)} 个重复图片路径，可能影响页码匹配准确性:")
            for path, descriptions in path_duplicates.items():
                logger.warning(f"  {path}: {len(descriptions)} 个描述")
        
        logger.info(f"从 {md_file_path} 中提取到 {len(image_descriptions)} 个图片描述")
        return image_descriptions
        
    except Exception as e:
        logger.error(f"读取 MD 文件失败 {md_file_path}: {e}")
        return {}


def generate_table_description(table_html: str) -> str:
    """
    从表格HTML内容生成描述
    
    Args:
        table_html: 表格HTML内容
        
    Returns:
        生成的表格描述
    """
    try:
        # 移除HTML标签，提取纯文本
        text_content = re.sub(r'<[^>]+>', '|', table_html)
        text_content = re.sub(r'\|+', '|', text_content).strip('|')
        
        # 分割成行
        rows = [row.strip() for row in text_content.split('|') if row.strip()]
        
        if not rows:
            return "数据表格"
        
        # 获取第一行作为表头信息
        first_row = rows[0] if rows else ""
        
        # 尝试识别表格类型
        if any(keyword in first_row for keyword in ['年度', '项目', '指标']):
            if any(keyword in first_row for keyword in ['2019', '2020', '2021', '2022']):
                return f"财务数据表格，包含{first_row}等指标的多年度对比数据"
            else:
                return f"数据对比表格，展示{first_row}等关键指标"
        
        # 尝试从表格内容推断描述
        if len(rows) > 1:
            second_row = rows[1] if len(rows) > 1 else ""
            if any(keyword in second_row for keyword in ['收入', '利润', '增长', '率']):
                return f"业绩数据表格，包含{first_row}等财务指标"
        
        # 默认描述
        return f"数据表格，包含{len(rows)}个数据项"
        
    except Exception as e:
        logger.debug(f"生成表格描述失败: {e}")
        return "数据表格"


def update_json_with_descriptions(json_file_path: str, image_descriptions: Dict[str, str], force_regenerate: bool = False) -> bool:
    """
    更新 JSON 文件中的图片和表格描述
    
    Args:
        json_file_path: JSON 文件路径
        image_descriptions: 图片描述字典
        force_regenerate: 是否强制重新生成表格描述
        
    Returns:
        是否成功更新
    """
    try:
        # 读取 JSON 文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            logger.error(f"JSON 文件格式错误，应为列表格式: {json_file_path}")
            return False
        
        updated_count = 0
        total_images = 0
        total_tables = 0
        image_updated = 0
        table_updated = 0
        
        # 遍历 JSON 数据，更新图片和表格描述
        for item in data:
            if isinstance(item, dict) and item.get('type') in ['image', 'table']:
                img_path = item.get('img_path', '')
                item_type = item.get('type')
                
                if item_type == 'image':
                    total_images += 1
                elif item_type == 'table':
                    total_tables += 1
                
                description_updated = False
                
                # 步骤1：优先使用MD文件中的描述（如果存在）
                md_description_used = False
                if img_path in image_descriptions:
                    description = image_descriptions[img_path]
                    caption_field = 'img_caption' if item_type == 'image' else 'table_caption'
                    
                    # 获取当前项目的页码信息用于验证
                    current_page = item.get('page_idx', -1) + 1  # JSON从0开始，转换为从1开始
                    
                    # 如果caption为空列表或不存在，则添加描述
                    if not item.get(caption_field):
                        item[caption_field] = [description]
                        description_updated = True
                        md_description_used = True
                        logger.info(f"✓ 更新{item_type}描述: {img_path} (页码:{current_page})")
                        logger.debug(f"  MD描述: {description[:100]}...")
                    else:
                        # 检查是否已有相同描述，避免重复添加
                        existing_captions = item[caption_field]
                        if description not in existing_captions:
                            existing_captions.append(description)
                            description_updated = True
                            md_description_used = True
                            logger.info(f"✓ 追加{item_type}描述: {img_path} (页码:{current_page})")
                        else:
                            md_description_used = True  # 已存在，算作已使用MD描述
                            logger.debug(f"⚠ MD描述已存在，跳过: {img_path} (页码:{current_page})")
                
                # 步骤2：对于表格，如果没有使用MD描述，则尝试生成描述
                if item_type == 'table' and not md_description_used:
                    existing_caption = item.get('table_caption', [])
                    has_valid_caption = existing_caption and any(cap.strip() for cap in existing_caption)
                    
                    # 强制重新生成或者没有有效描述时才处理
                    if force_regenerate or not has_valid_caption:
                        table_body = item.get('table_body', '')
                        if table_body:
                            generated_description = generate_table_description(table_body)
                            item['table_caption'] = [generated_description]
                            description_updated = True
                            action = "重新生成" if force_regenerate and has_valid_caption else "生成"
                            logger.info(f"✓ {action}表格描述: {img_path}")
                            logger.debug(f"  生成描述: {generated_description}")
                        else:
                            logger.debug(f"⚠ 表格无内容，无法生成描述: {img_path}")
                    else:
                        logger.debug(f"✓ 表格已有有效描述: {img_path}")
                
                # 步骤3：对于图片，如果没有使用MD描述且没有有效caption，记录日志
                elif item_type == 'image' and not md_description_used:
                    existing_caption = item.get('img_caption', [])
                    has_valid_caption = existing_caption and any(cap.strip() for cap in existing_caption)
                    
                    if not has_valid_caption:
                        logger.debug(f"⚠ 图片未找到描述: {img_path}")
                    else:
                        logger.debug(f"✓ 图片已有有效描述: {img_path}")
                
                else:
                    logger.debug(f"⚠ 未知类型: {item_type}, {img_path}")
                
                # 更新计数器
                if description_updated:
                    updated_count += 1
                    if item_type == 'image':
                        image_updated += 1
                    elif item_type == 'table':
                        table_updated += 1
        
        # 保存更新后的 JSON 文件
        if updated_count > 0:
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            logger.info(f"✓ 已保存更新后的文件: {json_file_path}")
        
        logger.info(f"📊 更新统计: 总图片={total_images}(更新{image_updated}), 总表格={total_tables}(更新{table_updated}), 总更新={updated_count}")
        return True
        
    except Exception as e:
        logger.error(f"更新 JSON 文件失败 {json_file_path}: {e}")
        return False


def process_single_project(project_dir: str, force_regenerate: bool = False) -> bool:
    """
    处理单个项目目录
    
    Args:
        project_dir: 项目目录路径
        force_regenerate: 是否强制重新生成表格描述
        
    Returns:
        是否成功处理
    """
    project_path = Path(project_dir)
    
    if not project_path.exists() or not project_path.is_dir():
        logger.error(f"项目目录不存在: {project_dir}")
        return False
    
    # 查找 full.md 文件
    md_file = project_path / 'full.md'
    if not md_file.exists():
        logger.error(f"未找到 full.md 文件: {md_file}")
        return False
    
    # 查找 *_content_list.json 文件
    json_files = list(project_path.glob('*_content_list.json'))
    if not json_files:
        logger.error(f"未找到 *_content_list.json 文件: {project_dir}")
        return False
    
    if len(json_files) > 1:
        logger.warning(f"找到多个 JSON 文件，使用第一个: {json_files[0]}")
    
    json_file = json_files[0]
    
    logger.info(f"🔄 处理项目: {project_path.name}")
    logger.info(f"  MD 文件: {md_file.name}")
    logger.info(f"  JSON 文件: {json_file.name}")
    
    # 提取图片描述
    image_descriptions = extract_image_descriptions_from_md(str(md_file))
    if not image_descriptions:
        logger.warning("未提取到任何图片描述，但继续处理表格")
        image_descriptions = {}  # 使用空字典继续处理
    
    # 更新 JSON 文件
    success = update_json_with_descriptions(str(json_file), image_descriptions, force_regenerate)
    
    if success:
        logger.info(f"✓ 项目处理完成: {project_path.name}")
    else:
        logger.error(f"✗ 项目处理失败: {project_path.name}")
    
    return success


def process_all_projects(data_dir: str, force_regenerate: bool = False) -> Tuple[int, int]:
    """
    处理所有项目
    
    Args:
        data_dir: 数据目录路径（包含 parser 子目录）
        force_regenerate: 是否强制重新生成表格描述
        
    Returns:
        (成功数量, 总数量)
    """
    parser_dir = Path(data_dir) / 'parser'
    
    if not parser_dir.exists():
        logger.error(f"parser 目录不存在: {parser_dir}")
        return 0, 0
    
    project_dirs = [d for d in parser_dir.iterdir() if d.is_dir()]
    
    if not project_dirs:
        logger.error(f"未找到任何项目目录: {parser_dir}")
        return 0, 0
    
    logger.info(f"🚀 开始处理 {len(project_dirs)} 个项目...")
    
    success_count = 0
    total_count = len(project_dirs)
    
    for project_dir in project_dirs:
        try:
            if process_single_project(str(project_dir), force_regenerate):
                success_count += 1
            logger.info(f"📊 进度: {success_count}/{total_count}")
            print("-" * 60)
        except Exception as e:
            logger.error(f"处理项目时出错 {project_dir}: {e}")
    
    logger.info(f"🎉 处理完成！成功: {success_count}/{total_count}")
    return success_count, total_count


def main():
    parser = argparse.ArgumentParser(description='合并图片描述到 JSON 文件')
    parser.add_argument('--project', '-p', type=str, help='处理单个项目目录')
    parser.add_argument('--data-dir', '-d', type=str, default='data', 
                       help='数据目录路径 (默认: data)')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='显示详细日志')
    parser.add_argument('--force-regenerate', '-f', action='store_true',
                       help='强制重新生成所有表格描述，即使已存在描述')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    if args.project:
        # 处理单个项目
        success = process_single_project(args.project, force_regenerate=args.force_regenerate)
        exit(0 if success else 1)
    else:
        # 处理所有项目
        success_count, total_count = process_all_projects(args.data_dir, force_regenerate=args.force_regenerate)
        exit(0 if success_count == total_count else 1)


if __name__ == '__main__':
    main()